This note is for Augment AI or any other similar AI coding agents. It records the necessary rules for working on this project by human. AI agents only read this file and never modifies it.

This project aims to build a full-stack AI-based application which helps people analyze data. The backend is implemented with LangGraph to reflect the data analysis steps. The `backend/src/langgraph_da_backend/dummy_agent.py` file is a working script that finishes the whole logic of analysis process.

Now, under the same directory of this file, there are 3 important directories: `frontend`, `backend`, and `gemini-fullstack-langgraph-quickstart`. The gemini directory is an example or template project, which should not be modified. It has been well written and working well. For uncertain functions or features, we should look at it as much as possible for reference.

The backend basically contains the langgraph applications, while frontend contains the frontend code. Our goal is to make the backend work with the frontend.

Constrains and requirements are as follows, make sure you follow them.

1. This project should NOT use langsmith payment API. We should make the whole frontend and backend manageable by us. Do not rely on black-boxed external services (except for the LLM calling API).
