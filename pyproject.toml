[project]
name = "langgraph-da-backend"
version = "0.1.0"
description = "Agentic data analysis assistant"
readme = "README.md"
authors = [
    { name = "BigCatc", email = "<EMAIL>" },
    { name = "ha<PERSON><PERSON>", email = "<EMAIL>" },
]
requires-python = ">=3.11"
dependencies = [
    "docstring-parser>=0.16",
    "hyperopt>=0.2.7",
    "lancedb>=0.23.0",
    "langchain[anthropic,google-genai,google-vertexai,openai]>=0.3.25",
    "langgraph>=0.4.8",
    "langgraph-checkpoint-sqlite>=2.0.10",
    "loguru>=0.7.3",
    "pandas-stubs>=2.2.3.250527",
    "pingouin>=0.5.5",
    "plotly>=6.2.0",
    "polars>=1.31.0",
    "python-multipart>=0.0.20",
    "statsforecast>=2.0.2",
    "statsmodels>=0.14.4",
    "types-python-dateutil>=2.9.0.20250516",
    "types-tqdm>=4.67.0.20250516",
    "xgboost-cpu>=3.0.2",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["backend/src/langgraph_da_backend"]
