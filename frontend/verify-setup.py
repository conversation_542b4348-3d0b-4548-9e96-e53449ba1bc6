#!/usr/bin/env python3
"""
Simple verification script to check if the frontend structure is correct.
Run this before installing Node.js dependencies.
"""

import os
import json
from pathlib import Path

def check_file_exists(file_path, description):
    """Check if a file exists and print status."""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (MISSING)")
        return False

def check_package_json():
    """Verify package.json has required dependencies."""
    try:
        with open('package.json', 'r') as f:
            package_data = json.load(f)
        
        required_deps = [
            '@langchain/langgraph-sdk',
            'react',
            'react-dom',
            'tailwindcss',
            'vite',
            'typescript'
        ]
        
        all_deps = {**package_data.get('dependencies', {}), **package_data.get('devDependencies', {})}
        
        print("\n📦 Package Dependencies:")
        for dep in required_deps:
            if dep in all_deps:
                print(f"✅ {dep}: {all_deps[dep]}")
            else:
                print(f"❌ {dep}: MISSING")
        
        return True
    except Exception as e:
        print(f"❌ Error reading package.json: {e}")
        return False

def main():
    print("🔍 Verifying Frontend Setup...")
    print("=" * 50)
    
    # Check main configuration files
    config_files = [
        ('package.json', 'Package configuration'),
        ('vite.config.ts', 'Vite configuration'),
        ('tsconfig.json', 'TypeScript configuration'),
        ('tailwind.config.js', 'Tailwind configuration'),
        ('index.html', 'HTML entry point'),
    ]
    
    print("\n📁 Configuration Files:")
    for file_path, description in config_files:
        check_file_exists(file_path, description)
    
    # Check source files
    src_files = [
        ('src/main.tsx', 'Main entry point'),
        ('src/App.tsx', 'Main App component'),
        ('src/global.css', 'Global styles'),
        ('src/lib/utils.ts', 'Utility functions'),
    ]
    
    print("\n📄 Source Files:")
    for file_path, description in src_files:
        check_file_exists(file_path, description)
    
    # Check components
    components = [
        ('src/components/WelcomeScreen.tsx', 'Welcome screen'),
        ('src/components/ChatMessagesView.tsx', 'Chat interface'),
        ('src/components/FileUpload.tsx', 'File upload'),
        ('src/components/InputForm.tsx', 'Input form'),
        ('src/components/ActivityTimeline.tsx', 'Activity timeline'),
        ('src/components/ImageDisplay.tsx', 'Image display'),
        ('src/components/ThemeProvider.tsx', 'Theme provider'),
        ('src/components/ThemeToggle.tsx', 'Theme toggle'),
    ]
    
    print("\n🧩 Components:")
    for file_path, description in components:
        check_file_exists(file_path, description)
    
    # Check UI components
    ui_components = [
        ('src/components/ui/button.tsx', 'Button component'),
        ('src/components/ui/textarea.tsx', 'Textarea component'),
        ('src/components/ui/scroll-area.tsx', 'Scroll area component'),
        ('src/components/ui/select.tsx', 'Select component'),
    ]
    
    print("\n🎨 UI Components:")
    for file_path, description in ui_components:
        check_file_exists(file_path, description)
    
    # Check package.json dependencies
    check_package_json()
    
    print("\n" + "=" * 50)
    print("✨ Frontend structure verification complete!")
    print("\n📋 Next Steps:")
    print("1. Install Node.js 18+ if not already installed")
    print("2. Run 'npm install' to install dependencies")
    print("3. Run 'npm run dev' to start the development server")
    print("4. Open http://localhost:3000 in your browser")

if __name__ == "__main__":
    main()
