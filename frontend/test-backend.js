#!/usr/bin/env node

/**
 * Simple script to test if the LangGraph backend is running
 * Run this with: node test-backend.js
 */

const http = require('http');

function testEndpoint(path, description) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 8000,
      path: path,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      console.log(`✅ ${description}: HTTP ${res.statusCode}`);
      resolve(true);
    });

    req.on('error', (err) => {
      console.log(`❌ ${description}: ${err.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`❌ ${description}: Request timeout`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function testBackend() {
  console.log('🔍 Testing LangGraph Backend Connection...\n');
  
  const tests = [
    { path: '/', description: 'Root endpoint' },
    { path: '/health', description: 'Health check endpoint' },
    { path: '/threads', description: 'Threads endpoint' },
    { path: '/docs', description: 'API documentation' }
  ];

  let anySuccess = false;
  
  for (const test of tests) {
    const success = await testEndpoint(test.path, test.description);
    if (success) anySuccess = true;
  }

  console.log('\n' + '='.repeat(50));
  
  if (anySuccess) {
    console.log('✅ Backend appears to be running on http://localhost:8000');
    console.log('💡 Try refreshing the frontend and submitting a query again.');
  } else {
    console.log('❌ Backend is not accessible on http://localhost:8000');
    console.log('\n📋 To fix this:');
    console.log('1. Make sure the LangGraph backend is running');
    console.log('2. Check if it\'s running on port 8000');
    console.log('3. Verify there are no firewall issues');
    console.log('4. Try running the backend with: python -m uvicorn app:app --host 0.0.0.0 --port 8000');
  }
}

testBackend().catch(console.error);
