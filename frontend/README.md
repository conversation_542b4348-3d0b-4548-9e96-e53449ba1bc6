# Data Analysis LangGraph Frontend

A modern React frontend for the Data Analysis LangGraph application. This frontend provides an intuitive interface for uploading CSV files and asking questions about your data, with real-time streaming of analysis results.

## Features

- 🎨 **Modern UI**: Clean, responsive design with dark/light mode support
- 📁 **File Upload**: Drag-and-drop CSV file upload with validation
- 💬 **Chat Interface**: Conversational interface for data analysis queries
- 📊 **Real-time Processing**: Live activity timeline showing analysis progress
- 🖼️ **Image Support**: Display generated charts and visualizations
- 🔄 **Streaming**: Real-time streaming of analysis results from LangGraph backend
- 🎯 **Smart Workflow**: Follows the backend workflow: standby → planning → engineering → analysis

## Tech Stack

- **React 19** with TypeScript
- **Vite** for fast development and building
- **TailwindCSS** for styling
- **LangGraph SDK** for backend integration
- **Radix UI** for accessible components
- **Lucide React** for icons

## Prerequisites

- Node.js 18+
- npm or yarn
- Running LangGraph backend (see backend setup instructions)

## Installation

1. **Install Node.js (if not already installed):**
   ```bash
   # On Ubuntu/Debian
   sudo apt update
   sudo apt install nodejs npm

   # Or install latest version via NodeSource
   curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

2. **Navigate to frontend directory:**
   ```bash
   cd frontend
   ```

3. **Install dependencies:**
   ```bash
   npm install
   ```

4. **Start the development server:**
   ```bash
   npm run dev
   ```
   The application will be available at `http://localhost:3000`

5. **Build for production:**
   ```bash
   npm run build
   ```

## Configuration

The frontend is configured to connect to the backend at:
- Development: `http://localhost:8000`
- Production: `http://localhost:8000`

To change the backend URL, update the `apiUrl` in `src/App.tsx`.

## Usage

1. **Start the application** and you'll see the welcome screen
2. **Upload a CSV file** using the file upload component
3. **Ask questions** about your data in natural language
4. **Watch the progress** through the activity timeline
5. **View results** including text analysis and generated visualizations

### Example Queries

- "What are the main trends in my data?"
- "How does the shift duration change over time?"
- "Show me the correlation between different variables"
- "What are the outliers in my dataset?"
- "Generate a summary report of my data"

## Project Structure

```
frontend/
├── src/
│   ├── components/          # React components
│   │   ├── ui/             # Base UI components
│   │   ├── ActivityTimeline.tsx
│   │   ├── ChatMessagesView.tsx
│   │   ├── FileUpload.tsx
│   │   ├── ImageDisplay.tsx
│   │   ├── InputForm.tsx
│   │   ├── ThemeProvider.tsx
│   │   ├── ThemeToggle.tsx
│   │   └── WelcomeScreen.tsx
│   ├── lib/                # Utilities
│   ├── App.tsx             # Main application component
│   ├── main.tsx            # Application entry point
│   └── global.css          # Global styles
├── public/                 # Static assets
├── package.json
├── vite.config.ts
└── tailwind.config.js
```

## Backend Integration

The frontend integrates with the LangGraph backend through:

1. **LangGraph SDK**: Uses `@langchain/langgraph-sdk` for streaming communication
2. **State Management**: Handles the backend's `AgentState` interface
3. **Event Mapping**: Maps backend workflow events to frontend timeline events
4. **File Handling**: Sends file paths to the backend for processing

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Adding New Components

1. Create component in `src/components/`
2. Export from appropriate index file
3. Import and use in other components

### Styling Guidelines

- Use TailwindCSS utility classes
- Follow the existing color scheme (CSS variables)
- Ensure dark mode compatibility
- Use consistent spacing and typography

## Troubleshooting

### Common Issues

1. **Backend Connection Failed**
   - Ensure the backend is running on the correct port
   - Check the `apiUrl` configuration in `App.tsx`

2. **File Upload Not Working**
   - Verify file is a valid CSV
   - Check file size (max 50MB)
   - Ensure proper file permissions

3. **Styling Issues**
   - Clear browser cache
   - Rebuild the project
   - Check TailwindCSS configuration

### Debug Mode

Enable debug logging by setting `NODE_ENV=development` and check browser console for detailed logs.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is part of the Data Analysis LangGraph application.
