import { useState, useEffect, useRef, useCallback } from 'react';

export interface ChatMessage {
  id: string;
  role: 'user' | 'ai';
  content: string;
  timestamp?: Date;
}

export interface ChatState {
  messages: ChatMessage[];
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
}

export interface ProgressEvent {
  type: 'progress';
  data: any;
}

export interface UseWebSocketChatOptions {
  apiUrl: string;
  onProgress?: (event: ProgressEvent) => void;
  onError?: (error: Error) => void;
  onStatus?: (status: string, message?: string) => void;
}

export function useWebSocketChat(options: UseWebSocketChatOptions) {
  const { apiUrl, onProgress, onError, onStatus } = options;

  const [state, setState] = useState<ChatState>({
    messages: [],
    isLoading: false,
    isConnected: false,
    error: null,
  });

  const wsRef = useRef<WebSocket | null>(null);
  const threadIdRef = useRef<string | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback((threadId: string) => {
    console.log('🔌 connect called with threadId:', threadId);

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('✅ WebSocket already connected');
      return;
    }

    threadIdRef.current = threadId;
    const wsUrl = `${apiUrl.replace('http', 'ws')}/ws/${threadId}`;
    console.log('🔗 Connecting to WebSocket URL:', wsUrl);

    try {
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('✅ WebSocket connected successfully');
        setState(prev => ({ ...prev, isConnected: true, error: null }));
        reconnectAttemptsRef.current = 0;

        // Request history when connected
        wsRef.current?.send(JSON.stringify({ type: 'get_history' }));
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          switch (data.type) {
            case 'message':
              setState(prev => ({
                ...prev,
                messages: [...prev.messages, {
                  id: data.id,
                  role: data.role,
                  content: data.content,
                  timestamp: new Date(),
                }],
              }));
              break;

            case 'history':
              setState(prev => ({
                ...prev,
                messages: data.messages.map((msg: any) => ({
                  ...msg,
                  timestamp: new Date(),
                })),
              }));
              break;

            case 'status':
              console.log('📊 Status update:', data);
              if (data.status === 'processing' || data.status === 'starting') {
                setState(prev => ({ ...prev, isLoading: true }));
              } else if (data.status === 'completed') {
                setState(prev => ({ ...prev, isLoading: false }));
              }
              // Call the onStatus callback if provided
              if (onStatus) {
                onStatus(data.status, data.message);
              }
              break;

            case 'progress':
              console.log('🔄 Progress update:', data);
              if (onProgress) {
                onProgress(data);
              }
              break;

            case 'error':
              console.error('❌ Error received:', data);

              // Create a more detailed error message
              let errorMessage = data.error || 'An unknown error occurred';

              // Add troubleshooting information if available
              if (data.troubleshooting && data.troubleshooting.length > 0) {
                errorMessage += '\n\nTroubleshooting steps:\n';
                data.troubleshooting.forEach((tip: string, index: number) => {
                  errorMessage += `${index + 1}. ${tip}\n`;
                });
              }

              // Add technical details if available (for debugging)
              if (data.technical_error && data.technical_error !== data.error) {
                console.error('Technical error details:', data.technical_error);
              }

              setState(prev => ({
                ...prev,
                error: errorMessage,
                isLoading: false
              }));

              if (onError) {
                const error = new Error(errorMessage);
                // Add additional properties for better error handling
                (error as any).errorType = data.error_type;
                (error as any).troubleshooting = data.troubleshooting;
                (error as any).technicalError = data.technical_error;
                onError(error);
              }
              break;
          }
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };

      wsRef.current.onclose = () => {
        console.log('WebSocket disconnected');
        setState(prev => ({ ...prev, isConnected: false }));

        // Attempt to reconnect
        if (reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current++;
          reconnectTimeoutRef.current = setTimeout(() => {
            if (threadIdRef.current) {
              connect(threadIdRef.current);
            }
          }, 1000 * Math.pow(2, reconnectAttemptsRef.current)); // Exponential backoff
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setState(prev => ({
          ...prev,
          error: 'Connection error occurred',
          isConnected: false
        }));
      };

    } catch (error) {
      console.error('Failed to create WebSocket:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to connect to server',
        isConnected: false
      }));
    }
  }, [apiUrl, onProgress, onError]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    threadIdRef.current = null;
    setState(prev => ({ ...prev, isConnected: false }));
  }, []);

  const sendMessage = useCallback((message: string, filePath?: string) => {
    console.log('🚀 sendMessage called with:', { message, filePath });
    console.log('📡 WebSocket state:', wsRef.current?.readyState);

    if (!wsRef.current) {
      console.error('❌ WebSocket not initialized');
      setState(prev => ({
        ...prev,
        error: 'WebSocket not initialized'
      }));
      return;
    }

    // If WebSocket is still connecting, wait for it to open
    if (wsRef.current.readyState === WebSocket.CONNECTING) {
      console.log('⏳ WebSocket still connecting, waiting...');

      const originalOnOpen = wsRef.current.onopen;
      wsRef.current.onopen = (event) => {
        console.log('✅ WebSocket connected, now sending message');
        if (originalOnOpen) originalOnOpen.call(wsRef.current, event);

        // Send the message now that we're connected
        const messageData = {
          type: 'chat',
          message,
          file_path: filePath,
        };
        console.log('📤 Sending WebSocket message:', messageData);
        wsRef.current!.send(JSON.stringify(messageData));
        setState(prev => ({ ...prev, error: null }));
      };
      return;
    }

    if (wsRef.current.readyState !== WebSocket.OPEN) {
      console.error('❌ WebSocket not connected, state:', wsRef.current.readyState);
      setState(prev => ({
        ...prev,
        error: 'Not connected to server'
      }));
      return;
    }

    const messageData = {
      type: 'chat',
      message,
      file_path: filePath,
    };

    console.log('📤 Sending WebSocket message:', messageData);
    wsRef.current.send(JSON.stringify(messageData));
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const clearMessages = useCallback(() => {
    setState(prev => ({ ...prev, messages: [] }));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    ...state,
    connect,
    disconnect,
    sendMessage,
    clearError,
    clearMessages,
  };
}
