import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Send, StopCircle, FileText } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { FileUpload } from "@/components/FileUpload";

interface InputFormProps {
  onSubmit: (query: string, filePath: string | null) => void;
  onCancel: () => void;
  isLoading: boolean;
  hasHistory: boolean;
}

export const InputForm: React.FC<InputFormProps> = ({
  onSubmit,
  onCancel,
  isLoading,
  hasHistory,
}) => {
  const [query, setQuery] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    setValidationError(null);

    if (!query.trim()) {
      setValidationError("Please enter a question about your data");
      return;
    }

    if (!selectedFile) {
      setValidationError("Please select a CSV file to analyze");
      return;
    }

    try {
      setIsUploading(true);
      console.log('📤 Starting file upload...');

      // Upload the file to the backend first
      const formData = new FormData();
      formData.append('file', selectedFile);

      const uploadResponse = await fetch('http://localhost:2024/upload', {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json();
        throw new Error(errorData.detail || 'File upload failed');
      }

      const uploadResult = await uploadResponse.json();
      console.log('✅ File uploaded:', uploadResult);

      // Use the server file path for processing
      const filePath = uploadResult.file_path;
      onSubmit(query, filePath);
      setQuery("");
      setSelectedFile(null);
      setShowFileUpload(false);
      setValidationError(null);

    } catch (error: any) {
      console.error('❌ File upload error:', error);
      setValidationError(`File upload failed: ${error.message}`);
    } finally {
      setIsUploading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const isSubmitDisabled = !query.trim() || !selectedFile || isLoading || isUploading;

  return (
    <form
      onSubmit={handleSubmit}
      className="flex flex-col gap-3 p-4 border-t border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-900"
    >
      {/* File Upload Section */}
      {showFileUpload && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Select CSV File</label>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowFileUpload(false)}
            >
              Cancel
            </Button>
          </div>
          <FileUpload
            onFileSelect={setSelectedFile}
            selectedFile={selectedFile}
          />
        </div>
      )}

      {/* Selected File Display */}
      {selectedFile && !showFileUpload && (
        <div className="flex items-center justify-between p-2 bg-gray-100 rounded-md dark:bg-gray-800">
          <div className="flex items-center space-x-2">
            <FileText className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium">{selectedFile.name}</span>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => setSelectedFile(null)}
          >
            Remove
          </Button>
        </div>
      )}

      {/* Main Input Area */}
      <div className="flex flex-col gap-2">
        <div className="flex items-end gap-2">
          <div className="flex-1 relative">
            <Textarea
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Ask a question about your data... (e.g., 'How does the shift duration change over time?')"
              className="min-h-[60px] max-h-[200px] resize-none pr-12"
              rows={2}
            />
          </div>

          {/* File Upload Button */}
          {!selectedFile && (
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => setShowFileUpload(true)}
              className="shrink-0"
            >
              <FileText className="h-4 w-4" />
            </Button>
          )}

          {/* Submit/Cancel Button */}
          {isLoading ? (
            <Button
              type="button"
              variant="destructive"
              size="icon"
              onClick={onCancel}
              className="shrink-0"
              title="Stop processing"
            >
              <StopCircle className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              type="submit"
              disabled={isSubmitDisabled}
              size="icon"
              className="shrink-0"
              title={
                isUploading
                  ? "Uploading file..."
                  : isSubmitDisabled
                    ? "Please enter a question and select a file"
                    : "Send message"
              }
            >
              <Send className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Validation Error */}
        {validationError && (
          <div className="flex items-center gap-2 text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-2">
            <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span>{validationError}</span>
          </div>
        )}

        {/* Helper Text */}
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>
            {selectedFile
              ? `File selected: ${selectedFile.name}`
              : "Select a CSV file to analyze your data"
            }
          </span>
          <span>Ctrl+Enter to send</span>
        </div>
      </div>
    </form>
  );
};
