import React from "react";
import {
  <PERSON>,
  <PERSON>Text,
  <PERSON><PERSON><PERSON>,
  BarChart3,
  CheckCircle,
  Loader2,
  Database,
  Brain
} from "lucide-react";
import { cn } from "@/lib/utils";

export interface ProcessedEvent {
  title: string;
  data: string;
  timestamp?: string;
  status?: "pending" | "running" | "completed" | "error";
}

interface ActivityTimelineProps {
  events: ProcessedEvent[];
  className?: string;
}

export const ActivityTimeline: React.FC<ActivityTimelineProps> = ({
  events,
  className,
}) => {
  const getEventIcon = (title: string, status?: string) => {
    const iconClass = "h-4 w-4";

    if (status === "running") {
      return <Loader2 className={cn(iconClass, "animate-spin text-blue-500")} />;
    }

    if (status === "completed") {
      return <CheckCircle className={cn(iconClass, "text-green-500")} />;
    }

    if (status === "error") {
      return <CheckCircle className={cn(iconClass, "text-red-500")} />;
    }

    // Default icons based on title/stage
    const titleLower = title.toLowerCase();

    if (titleLower.includes("standby") || titleLower.includes("loading")) {
      return <Clock className={cn(iconClass, "text-gray-500")} />;
    }

    if (titleLower.includes("plan") || titleLower.includes("planning")) {
      return <Brain className={cn(iconClass, "text-purple-500")} />;
    }

    if (titleLower.includes("engineering") || titleLower.includes("processing") || titleLower.includes("cleaning")) {
      return <Settings className={cn(iconClass, "text-orange-500")} />;
    }

    if (titleLower.includes("analysis") || titleLower.includes("analyzing")) {
      return <BarChart3 className={cn(iconClass, "text-blue-500")} />;
    }

    if (titleLower.includes("data") || titleLower.includes("loading")) {
      return <Database className={cn(iconClass, "text-green-500")} />;
    }

    return <FileText className={cn(iconClass, "text-gray-500")} />;
  };

  const getEventColor = (title: string, status?: string) => {
    if (status === "running") return "border-blue-500 bg-blue-50 dark:bg-blue-950/20";
    if (status === "completed") return "border-green-500 bg-green-50 dark:bg-green-950/20";
    if (status === "error") return "border-red-500 bg-red-50 dark:bg-red-950/20";

    const titleLower = title.toLowerCase();

    if (titleLower.includes("plan")) return "border-purple-500 bg-purple-50 dark:bg-purple-950/20";
    if (titleLower.includes("engineering")) return "border-orange-500 bg-orange-50 dark:bg-orange-950/20";
    if (titleLower.includes("analysis")) return "border-blue-500 bg-blue-50 dark:bg-blue-950/20";

    return "border-gray-300 bg-gray-50 dark:bg-gray-800";
  };

  if (events.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-3", className)}>
      <div className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
        Processing Steps
      </div>

      <div className="space-y-2">
        {events.map((event, index) => (
          <div
            key={index}
            className={cn(
              "flex items-start gap-3 p-3 rounded-lg border transition-colors",
              getEventColor(event.title, event.status)
            )}
          >
            <div className="flex-shrink-0 mt-0.5">
              {getEventIcon(event.title, event.status)}
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {event.title}
                </h4>
                {event.timestamp && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {event.timestamp}
                  </span>
                )}
              </div>

              {event.data && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 break-words">
                  {event.data}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
