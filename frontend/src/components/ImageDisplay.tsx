import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Download, Maximize2, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface ImageDisplayProps {
  src: string;
  alt?: string;
  caption?: string;
  className?: string;
}

export const ImageDisplay: React.FC<ImageDisplayProps> = ({
  src,
  alt = "Generated visualization",
  caption,
  className,
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const handleDownload = async () => {
    try {
      const response = await fetch(src);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `visualization-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download image:', error);
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  if (hasError) {
    return (
      <div className={cn("border rounded-lg p-4 bg-muted", className)}>
        <div className="flex items-center justify-center h-32 text-muted-foreground">
          <div className="text-center">
            <p className="text-sm">Failed to load image</p>
            <p className="text-xs mt-1">{src}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={cn("relative border rounded-lg overflow-hidden bg-muted", className)}>
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
        
        <img
          src={src}
          alt={alt}
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={cn(
            "w-full h-auto transition-opacity",
            isLoading ? "opacity-0" : "opacity-100"
          )}
        />
        
        {!isLoading && !hasError && (
          <div className="absolute top-2 right-2 flex gap-1">
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 bg-black/50 hover:bg-black/70 text-white border-0"
              onClick={toggleFullscreen}
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 bg-black/50 hover:bg-black/70 text-white border-0"
              onClick={handleDownload}
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        )}
        
        {caption && (
          <div className="p-3 bg-background border-t">
            <p className="text-sm text-muted-foreground">{caption}</p>
          </div>
        )}
      </div>

      {/* Fullscreen Modal */}
      {isFullscreen && (
        <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4">
          <div className="relative max-w-full max-h-full">
            <Button
              variant="secondary"
              size="icon"
              className="absolute top-4 right-4 z-10 bg-black/50 hover:bg-black/70 text-white border-0"
              onClick={toggleFullscreen}
            >
              <X className="h-4 w-4" />
            </Button>
            <img
              src={src}
              alt={alt}
              className="max-w-full max-h-full object-contain"
            />
          </div>
        </div>
      )}
    </>
  );
};
