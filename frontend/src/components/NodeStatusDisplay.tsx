import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, AlertCircle, Play } from 'lucide-react';

export interface NodeStatus {
  name: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  description: string;
}

interface NodeStatusDisplayProps {
  currentNode?: string;
  nodeStatuses: NodeStatus[];
  error?: string | null;
}

const getStatusIcon = (status: NodeStatus['status']) => {
  switch (status) {
    case 'completed':
      return <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />;
    case 'running':
      return <Play className="h-4 w-4 text-blue-500 dark:text-blue-400 animate-pulse" />;
    case 'error':
      return <AlertCircle className="h-4 w-4 text-red-500 dark:text-red-400" />;
    default:
      return <Clock className="h-4 w-4 text-muted-foreground" />;
  }
};

const getStatusColor = (status: NodeStatus['status']) => {
  switch (status) {
    case 'completed':
      return 'bg-green-500/10 text-green-600 dark:text-green-400 border-green-200 dark:border-green-800';
    case 'running':
      return 'bg-blue-500/10 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-800';
    case 'error':
      return 'bg-red-500/10 text-red-600 dark:text-red-400 border-red-200 dark:border-red-800';
    default:
      return 'bg-muted/50 text-muted-foreground border-muted';
  }
};

export const NodeStatusDisplay: React.FC<NodeStatusDisplayProps> = ({
  currentNode,
  nodeStatuses,
  error
}) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <div className="h-2 w-2 bg-blue-500 dark:bg-blue-400 rounded-full animate-pulse" />
          Processing Pipeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {nodeStatuses.map((node, index) => (
            <div
              key={node.name}
              className={`flex items-center gap-3 p-3 rounded-lg border transition-all duration-200 ${node.status === 'running'
                ? 'bg-blue-500/5 dark:bg-blue-500/10 border-blue-200 dark:border-blue-800 shadow-sm'
                : 'bg-muted/30 dark:bg-muted/20 border-muted dark:border-muted'
                }`}
            >
              <div className="flex-shrink-0">
                {getStatusIcon(node.status)}
              </div>

              <div className="flex-grow min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-sm text-foreground">{node.name}</h4>
                  <Badge
                    variant="outline"
                    className={`text-xs ${getStatusColor(node.status)}`}
                  >
                    {node.status}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">{node.description}</p>
              </div>

              {node.status === 'running' && (
                <div className="flex-shrink-0">
                  <div className="h-2 w-2 bg-blue-500 dark:bg-blue-400 rounded-full animate-ping" />
                </div>
              )}
            </div>
          ))}

          {error && (
            <div className="mt-4 p-3 bg-red-500/10 dark:bg-red-500/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-500 dark:text-red-400" />
                <span className="text-sm font-medium text-red-600 dark:text-red-400">Error</span>
              </div>
              <p className="text-sm text-red-600 dark:text-red-300 mt-1">{error}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
