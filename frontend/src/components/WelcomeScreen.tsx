import React from "react";
import { InputForm } from "@/components/InputForm";
import { BarChart3, FileSpreadsheet, Brain, Zap } from "lucide-react";

interface WelcomeScreenProps {
  handleSubmit: (query: string, filePath: string | null) => void;
  isLoading: boolean;
  onCancel: () => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  handleSubmit,
  isLoading,
  onCancel,
}) => {
  const features = [
    {
      icon: FileSpreadsheet,
      title: "CSV Data Analysis",
      description: "Upload your CSV files and get instant insights",
    },
    {
      icon: Brain,
      title: "AI-Powered Insights",
      description: "Advanced data analysis using LangGraph workflows",
    },
    {
      icon: BarChart3,
      title: "Visual Analytics",
      description: "Generate charts and visualizations automatically",
    },
    {
      icon: Zap,
      title: "Real-time Processing",
      description: "Stream results as your data is being analyzed",
    },
  ];

  const exampleQueries = [
    "What are the main trends in my data?",
    "How does the shift duration change over time?",
    "Show me the correlation between different variables",
    "What are the outliers in my dataset?",
    "Generate a summary report of my data",
  ];

  return (
    <div className="flex flex-col h-full min-h-0">
      {/* Header */}
      <div className="flex-1 flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8 text-center overflow-y-auto">
        <div className="w-full max-w-4xl mx-auto">
          <div className="mb-6">
            <BarChart3 className="h-12 w-12 mx-auto mb-3 text-blue-500" />
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Data Analysis Assistant
            </h1>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 dark:text-gray-400 mb-6">
              Upload your CSV data and ask questions to get AI-powered insights and visualizations
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {features.map((feature, index) => (
              <div
                key={index}
                className="p-3 rounded-lg border border-gray-200 bg-white hover:shadow-md transition-shadow dark:border-gray-700 dark:bg-gray-800"
              >
                <feature.icon className="h-6 w-6 text-blue-500 mb-2" />
                <h3 className="font-semibold mb-1 text-sm">{feature.title}</h3>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>

          {/* Example Queries */}
          <div className="mb-6">
            <h3 className="text-base font-semibold mb-3">Example Questions</h3>
            <div className="flex flex-wrap gap-2 justify-center max-w-3xl mx-auto">
              {exampleQueries.map((query, index) => (
                <button
                  key={index}
                  onClick={() => handleSubmit(query, null)}
                  className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-full transition-colors dark:bg-gray-800 dark:hover:bg-gray-700"
                  disabled={isLoading}
                >
                  "{query}"
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Input Form */}
      <div className="border-t border-gray-200 dark:border-gray-700 flex-shrink-0">
        <InputForm
          onSubmit={handleSubmit}
          onCancel={onCancel}
          isLoading={isLoading}
          hasHistory={false}
        />
      </div>
    </div>
  );
};
