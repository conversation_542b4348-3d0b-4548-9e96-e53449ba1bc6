import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { InputForm } from "@/components/InputForm";
import { ActivityTimeline, ProcessedEvent } from "@/components/ActivityTimeline";
import { ImageDisplay } from "@/components/ImageDisplay";
import ReactMarkdown from "react-markdown";
import { User, Bot, FileText } from "lucide-react";
import type { ChatMessage } from "@/hooks/useWebSocketChat";

interface ChatMessagesViewProps {
  messages: ChatMessage[];
  isLoading: boolean;
  scrollAreaRef: React.RefObject<HTMLDivElement>;
  onSubmit: (query: string, filePath: string | null) => void;
  onCancel: () => void;
  liveActivityEvents: ProcessedEvent[];
  historicalActivities: Record<string, ProcessedEvent[]>;
}

export const ChatMessagesView: React.FC<ChatMessagesViewProps> = ({
  messages,
  isLoading,
  scrollAreaRef,
  onSubmit,
  onCancel,
  liveActivityEvents,
  historicalActivities,
}) => {
  // Function to extract image URLs from message content
  const extractImages = (content: string): { images: string[], textContent: string } => {
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
    const images: string[] = [];
    let match;

    while ((match = imageRegex.exec(content)) !== null) {
      images.push(match[2]); // URL is in the second capture group
    }

    // Remove image markdown from text content
    const textContent = content.replace(imageRegex, '').trim();

    return { images, textContent };
  };
  const renderMessage = (message: ChatMessage, index: number) => {
    const isUser = message.role === "user";
    const isLast = index === messages.length - 1;
    const messageActivities = message.id ? historicalActivities[message.id] : [];

    // Extract images from AI messages
    const { images, textContent } = !isUser ? extractImages(message.content) : { images: [], textContent: message.content };

    return (
      <div key={message.id || index} className="mb-6">
        <div className={`flex gap-3 ${isUser ? "justify-end" : "justify-start"}`}>
          {!isUser && (
            <div className="flex-shrink-0">
              <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                <Bot className="w-4 h-4 text-white" />
              </div>
            </div>
          )}

          <div className={`max-w-[85%] sm:max-w-[80%] ${isUser ? "order-first" : ""}`}>
            {/* Text content */}
            {textContent && (
              <div
                className={`rounded-lg px-4 py-3 ${isUser
                  ? "bg-blue-500 text-white ml-auto"
                  : "bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-100"
                  }`}
              >
                {isUser ? (
                  <p className="whitespace-pre-wrap">{textContent}</p>
                ) : (
                  <div className="prose prose-sm dark:prose-invert max-w-none">
                    <ReactMarkdown>{textContent}</ReactMarkdown>
                  </div>
                )}
              </div>
            )}

            {/* Images */}
            {images.length > 0 && (
              <div className={`space-y-3 ${textContent ? "mt-3" : ""}`}>
                {images.map((imageUrl, imgIndex) => (
                  <ImageDisplay
                    key={imgIndex}
                    src={imageUrl}
                    alt={`Generated visualization ${imgIndex + 1}`}
                    caption={`Visualization ${imgIndex + 1}`}
                  />
                ))}
              </div>
            )}

            {/* Show activity timeline for AI messages */}
            {!isUser && messageActivities.length > 0 && (
              <div className="mt-3">
                <ActivityTimeline events={messageActivities} />
              </div>
            )}
          </div>

          {isUser && (
            <div className="flex-shrink-0">
              <div className="w-8 h-8 rounded-full bg-gray-500 flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full min-h-0">
      {/* Messages Area */}
      <ScrollArea className="flex-1 p-2 sm:p-4 min-h-0" ref={scrollAreaRef}>
        <div className="space-y-4">
          {messages.map((message, index) => renderMessage(message, index))}

          {/* Live Activity Timeline */}
          {isLoading && liveActivityEvents.length > 0 && (
            <div className="flex gap-3 justify-start">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
              </div>
              <div className="max-w-[80%]">
                <div className="rounded-lg px-4 py-3 bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-100">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium">Processing...</span>
                  </div>
                  <ActivityTimeline events={liveActivityEvents} />
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Input Form */}
      <InputForm
        onSubmit={onSubmit}
        onCancel={onCancel}
        isLoading={isLoading}
        hasHistory={messages.length > 0}
      />
    </div>
  );
};
