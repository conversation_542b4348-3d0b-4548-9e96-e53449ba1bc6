@import "tailwindcss";

/* Base styles */
* {
  border-color: rgb(229 231 235);
  /* gray-200 */
}

body {
  background-color: white;
  color: rgb(17 24 39);
  /* gray-900 */
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Dark mode */
.dark * {
  border-color: rgb(75 85 99);
  /* gray-600 */
}

.dark body {
  background-color: rgb(17 24 39);
  /* gray-900 */
  color: rgb(243 244 246);
  /* gray-100 */
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgb(209 213 219);
  /* gray-300 */
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(156 163 175);
  /* gray-400 */
}

.dark ::-webkit-scrollbar-thumb {
  background: rgb(75 85 99);
  /* gray-600 */
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128);
  /* gray-500 */
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}