# database_manager.py
import hashlib
import json
from typing import List, Optional

import lancedb
import lancedb.pydantic
from lancedb.embeddings import TextEmbeddingFunction
from langgraph_da_backend.expert_tools import *
from langgraph_da_backend.utils.static import RAG_DB_CACHE
from langgraph_da_backend.utils.tool.tool_card import (
    REGISTERED_TOOLS,
    FunctionTool,
)
from langgraph_da_backend.vector_search.embed import CustomEmbeddingFunction
from loguru import logger


class DatabaseManager:
    """Handles the creation and incremental updating of the function vector database."""

    def __init__(self, embedding_function: TextEmbeddingFunction):
        self.db = lancedb.connect(RAG_DB_CACHE.resolve().as_posix())
        self.embedding_function = embedding_function

        # Define the table schema using the provided embedding function
        class FunctionToolDBSchema(lancedb.pydantic.LanceModel):
            text: str = self.embedding_function.SourceField()
            vector: lancedb.pydantic.Vector(self.embedding_function.ndims()) = self.embedding_function.VectorField()  # type: ignore
            name: str
            source_hash: str
            tool_schema: str

        self.schema = FunctionToolDBSchema

    def _calculate_hash(self, content: str) -> str:
        """Calculates a SHA256 hash for the given content."""
        return hashlib.sha256(content.encode("utf-8")).hexdigest()

    def create_or_update_database(
        self, table_name: str, keyword_filters: Optional[List[str]] = None
    ):
        """
        Creates a new DB table or incrementally updates an existing one.
        - Adds new functions.
        - Updates functions whose schema or description has changed.
        - Removes functions that are no longer registered.
        """
        logger.debug("\n--- Starting Database Sync ---")

        if table_name not in self.db.table_names():
            logger.debug(f"Table '{table_name}' not found. Creating a new one.")
            table = self.db.create_table(table_name, schema=self.schema)
        else:
            logger.debug(
                f"Found existing table '{table_name}'. Checking for updates..."
            )
            table = self.db.open_table(table_name)

        # 1. Get current state from the database
        try:
            db_records = table.to_pandas()
            existing_funcs = {
                row["name"]: row["source_hash"]
                for _, row in db_records.iterrows()
            }
        except Exception:  # Table is empty or other read error
            existing_funcs = {}

        # 2. Get desired state from the code registry
        registry_tools = {tool.name: tool for tool in REGISTERED_TOOLS}
        if keyword_filters:
            registry_tools = {
                name: tool
                for name, tool in registry_tools.items()
                if all(
                    [
                        not keyword in tool.keywords
                        for keyword in keyword_filters
                    ]
                )
            }

        # 3. Determine what needs to be added or updated
        tools_to_add = []
        for name, tool in registry_tools.items():
            schema_str = json.dumps(tool.tool_schema)
            embedding_text = schema_str
            current_hash = self._calculate_hash(embedding_text)

            # If function is new or has changed, add it to the list
            if (
                name not in existing_funcs
                or existing_funcs[name] != current_hash
            ):
                logger.debug(
                    f"  - Staging update/insert for '{name}' (reason: {'new' if name not in existing_funcs else 'changed'})"
                )
                tools_to_add.append(
                    {
                        "text": embedding_text,
                        "name": name,
                        "source_hash": current_hash,
                        "tool_schema": schema_str,
                    }
                )
                # If it's an update, we must first delete the old record
                if name in existing_funcs:
                    table.delete(f"name = '{name}'")

        # 4. Determine what needs to be deleted
        funcs_to_delete = set(existing_funcs.keys()) - set(
            registry_tools.keys()
        )
        if funcs_to_delete:
            for name in funcs_to_delete:
                logger.debug(
                    f"  - Staging delete for '{name}' (reason: removed from registry)"
                )
                table.delete(f"name = '{name}'")

        # 5. Perform the batched ADD operation
        if tools_to_add:
            table.add(tools_to_add)
            logger.debug(f"\nAdded/Updated {len(tools_to_add)} records.")

        if not tools_to_add and not funcs_to_delete:
            logger.debug("Database is already up-to-date. No changes made.")

        logger.debug("--- Database Sync Complete ---")
        return table


if __name__ == "__main__":
    embed_func = CustomEmbeddingFunction.create(
        name="text-embedding-3-large",
        base_url="https://vip.apiyi.com/v1",
    )

    manager = DatabaseManager(embed_func)
    function_table = manager.create_or_update_database(
        "dev", keyword_filters=["dev", "disabled"]
    )

    QUERY = "Distinguish the different patterns of incident failures."

    if function_table and len(function_table):
        results = (
            function_table.search(f"{QUERY}")
            .limit(2)
            .to_pydantic(manager.schema)
        )
        print("\n--- Sample Query Result ---")
        print(f"Query: '{QUERY}'")
        print(f"1st Match: {results[0].name}")
        print(f"2nd Match: {results[1].name}")
        print("---------------------------")
