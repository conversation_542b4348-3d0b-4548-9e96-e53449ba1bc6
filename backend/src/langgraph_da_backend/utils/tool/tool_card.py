import inspect
import json
from dataclasses import dataclass, field
from functools import wraps
from typing import (
    Annotated,
    Any,
    Callable,
    Dict,
    List,
    Optional,
    get_args,
    get_origin,
    get_type_hints,
)

from pydantic import BaseModel, Field, field_validator


@dataclass(frozen=True)
class FunctionTool:
    """
    A class that encapsulates a callable function and its enriched metadata.

    This provides a clean, type-safe structure instead of attaching
    dunder attributes to a function object.
    """

    name: str
    description: str
    keywords: List[str]
    tool_schema: Dict[str, Any]
    callable: Callable

    # Store the original signature for introspection
    original_signature: inspect.Signature = field(repr=False)

    def __call__(self, *args, **kwargs):
        """Makes the object itself callable, just like a normal function."""
        return self.callable(*args, **kwargs)

    def __repr__(self):
        return f"<FunctionTool(name='{self.name}')>"


REGISTERED_TOOLS: List[FunctionTool] = []


def _format_type_annotation(py_type) -> str:
    """Converts a Python type annotation into a descriptive string."""
    # For types from the `typing` module, str() gives a good representation
    if get_origin(py_type) is not None:
        # Stringify complex types like `typing.Dict[str, int]`
        return str(py_type)
    # For basic types, use the __name__ attribute (e.g., 'int', 'str')
    if hasattr(py_type, "__name__"):
        return py_type.__name__
    # Fallback for other cases
    return str(py_type)


def _generate_tool_schema(func: Callable, description: str) -> dict:
    """
    Generates a JSON-like schema for a function, preserving the
    full Python type annotations as strings.
    """
    sig = inspect.signature(func)
    try:
        type_hints = get_type_hints(func, include_extras=True)
    except Exception:
        type_hints = {}

    properties = {}
    required = []

    for param in sig.parameters.values():
        if param.default is inspect.Parameter.empty:
            required.append(param.name)

        param_schema = {}
        actual_type = type_hints.get(param.name, param.annotation)

        if get_origin(actual_type) is Annotated:
            type_args = get_args(actual_type)
            actual_type = type_args[0]
            for ann in type_args[1:]:
                if isinstance(ann, str):
                    param_schema["description"] = ann
                    break

        param_schema["type"] = _format_type_annotation(actual_type)
        properties[param.name] = param_schema

    # * NOTE: current schema implementation simply follows the MCP standard
    # * but I find it somewhat redundant; in the future we may adjust the schema
    tool_schema = {
        "name": func.__name__,
        "description": description,
        "parameters": {"type": "object", "properties": properties},
    }
    if required:
        tool_schema["parameters"]["required"] = required  # type: ignore

    return tool_schema


def tool_card(keywords: List[str], description: Optional[str] = None):
    """
    A decorator that enriches a function with metadata and a structured
    schema, preserving full Python type hints for robust parsing.
    """

    def decorator(func: Callable) -> FunctionTool:
        docstring = inspect.getdoc(func)
        final_description = description or (
            docstring.split("\n\n")[0] if docstring else None
        )

        if not final_description:
            raise ValueError(
                f"No description was provided and no docstring could be found for function '{func.__name__}'."
            )

        # Generate metadata
        tool_schema = _generate_tool_schema(func, final_description)
        original_sig = inspect.signature(func)

        return FunctionTool(
            name=func.__name__,
            description=final_description,
            keywords=keywords,
            tool_schema=tool_schema,
            callable=func,
            original_signature=original_sig,
        )

    return decorator


def register_tool(func: FunctionTool):
    """
    A simple decorator that adds a function to the global TOOL_REGISTRY.

    This decorator should be applied *after* @enrich to ensure the function
    has the necessary metadata attached when it's registered.
    """
    if not hasattr(func, "tool_schema"):
        raise TypeError(
            f"Function '{func.name}' cannot be registered. "
            "It must be decorated with @tool_card before @register_tool."
        )
    REGISTERED_TOOLS.append(func)
    return func


@register_tool
@tool_card(keywords=["example", "test", "dev"])
def example_function(
    a: Annotated[int, "The first number"],
    meaningful_para: str,
    complex_structure: Annotated[
        dict,
        "A complex structure which does not have fix keys",
    ],
    default_para: float = 3.14,
):
    """
    This is an example function with a docstring and annotations.
    This function actually does nothing and is non-sense, so never pick it as a real tool.
    """
    pass


if __name__ == "__main__":
    print(REGISTERED_TOOLS)
    print(REGISTERED_TOOLS[0].tool_schema)
