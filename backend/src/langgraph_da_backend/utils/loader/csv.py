from pathlib import Path
from typing import Union

import polars as pl
from langgraph_da_backend.utils.loader.post_cast import (
    _post_cast_currency_cols,
    _post_cast_datetime_cols,
)


def _load_csv(obj_file: str):
    """
    Dummy method for trigger the csv loading by polars.
    """
    return pl.scan_csv(
        obj_file,
        null_values=["null", "None", "NULL", "nan", "NaN"],
        try_parse_dates=True,
        ignore_errors=True,
    )


def load(file_path: Union[str, Path]) -> pl.LazyFrame:
    if not Path(file_path).resolve().exists():
        raise FileNotFoundError(
            f"the given file path `{file_path}` does not exist, please check."
        )

    try:
        lazydf = _load_csv(Path(file_path).resolve().as_posix())
        # post process, try casting datetime cols
        lazydf = _post_cast_datetime_cols(lazydf)
        lazydf = _post_cast_currency_cols(lazydf)
    except:
        raise

    return lazydf
