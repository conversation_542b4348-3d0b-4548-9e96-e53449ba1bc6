from typing import List, Optional, Tuple, Annotated, Dict, Any
from pathlib import Path

from ..utils.tool.tool_card import tool_card, register_tool

def _save_plot(fig, output_dir: str, filename: str) -> str:
    """Saves a Matplotlib figure to a file and closes it."""
    import matplotlib.pyplot as plt  # type: ignore
    
    p_output = Path(output_dir)
    p_output.mkdir(parents=True, exist_ok=True)
    output_path = p_output / filename
    
    try:
        fig.savefig(output_path, format="png", bbox_inches="tight")
        return str(output_path)
    finally:
        plt.close(fig)

def _read_data_to_pandas(input_file_path: str):
    """Reads a CSV or Parquet file into a pandas DataFrame."""
    import polars as pl  # type: ignore
    p_input = Path(input_file_path)
    if not p_input.exists():
        raise FileNotFoundError(f"Input file not found: {input_file_path}")
    
    if p_input.suffix == '.parquet':
        return pl.read_parquet(p_input).to_pandas()
    else:
        return pl.read_csv(p_input).to_pandas()

@register_tool
@tool_card(
    keywords=["plot", "histogram", "distribution"],
    description="Generates a histogram for a single numerical column to show its distribution."
)
def create_histogram(
    input_file_path: Annotated[str, "Path to the input data file (CSV or Parquet)."],
    output_dir: Annotated[str, "Directory to save the plot image."],
    column: Annotated[str, "The numerical column to plot."],
    bins: Annotated[Optional[int], "Number of bins in the histogram."] = None,
    show_kde: Annotated[bool, "Whether to overlay a Kernel Density Estimate curve."] = True,
    title: Annotated[Optional[str], "Custom title for the plot."] = None,
) -> Dict[str, Any]:
    """
    Creates and saves a histogram for a specified numerical column in a dataset.
    This is useful for understanding the distribution of a single variable.
    """
    try:
        import matplotlib.pyplot as plt  # type: ignore
        import seaborn as sns  # type: ignore
        import pandas as pd  # type: ignore

        sns.set_style("whitegrid")
        plt.switch_backend("Agg")

        df = _read_data_to_pandas(input_file_path)
        
        if column not in df.columns:
            return {"error": f"Column '{column}' not found."}
        if not pd.api.types.is_numeric_dtype(df[column]):
            return {"error": f"Column '{column}' is not numeric."}

        fig, ax = plt.subplots(figsize=(10, 6))
        sns.histplot(data=df, x=column, bins=bins if bins else "auto", kde=show_kde, ax=ax)
        
        plot_title = title if title else f"Distribution of {column}"
        ax.set_title(plot_title)
        ax.set_xlabel(column)
        ax.set_ylabel("Frequency")
        fig.tight_layout()
        
        output_path = _save_plot(fig, output_dir, f"histogram_{column}.png")
        return {"status": "success", "image_path": output_path, "description": plot_title}
    except Exception as e:
        return {"error": f"Failed to create histogram: {str(e)}"}

@register_tool
@tool_card(
    keywords=["plot", "scatter", "relationship", "correlation"],
    description="Generates a scatter plot to visualize the relationship between two numerical columns."
)
def create_scatter_plot(
    input_file_path: Annotated[str, "Path to the input data file (CSV or Parquet)."],
    output_dir: Annotated[str, "Directory to save the plot image."],
    x_col: Annotated[str, "The column for the X-axis."],
    y_col: Annotated[str, "The column for the Y-axis."],
    hue_col: Annotated[Optional[str], "Optional categorical column to color points by."] = None,
    title: Annotated[Optional[str], "Custom title for the plot."] = None,
) -> Dict[str, Any]:
    """
    Creates and saves a scatter plot to show the relationship between two variables.
    An optional third variable can be used to color the points.
    """
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns

        sns.set_style("whitegrid")
        plt.switch_backend("Agg")

        df = _read_data_to_pandas(input_file_path)
        
        fig, ax = plt.subplots(figsize=(10, 6))
        sns.scatterplot(data=df, x=x_col, y=y_col, hue=hue_col, ax=ax)
        
        plot_title = title if title else f"Scatter Plot of {y_col} vs. {x_col}"
        ax.set_title(plot_title)
        ax.set_xlabel(x_col)
        ax.set_ylabel(y_col)
        fig.tight_layout()

        filename = f"scatter_{y_col}_vs_{x_col}.png"
        output_path = _save_plot(fig, output_dir, filename)
        return {"status": "success", "image_path": output_path, "description": plot_title}
    except Exception as e:
        return {"error": f"Failed to create scatter plot: {str(e)}"}

@register_tool
@tool_card(
    keywords=["plot", "bar chart", "categorical", "count"],
    description="Generates a bar plot for categorical data, showing counts or aggregated numerical values."
)
def create_bar_plot(
    input_file_path: Annotated[str, "Path to the input data file (CSV or Parquet)."],
    output_dir: Annotated[str, "Directory to save the plot image."],
    x_col: Annotated[str, "The categorical column for the X-axis."],
    y_col: Annotated[Optional[str], "Optional numerical column for the Y-axis. If not provided, a count plot is created."] = None,
    hue_col: Annotated[Optional[str], "Optional categorical column for color grouping."] = None,
    estimator: Annotated[str, "Aggregation function ('mean', 'sum', 'median') if y_col is provided."] = "mean",
    title: Annotated[Optional[str], "Custom title for the plot."] = None,
) -> Dict[str, Any]:
    """
    Creates a bar plot. If only x_col is given, it's a count plot. 
    If y_col (numeric) is also given, it shows the aggregated value of y_col for each category in x_col.
    """
    try:
        import matplotlib.pyplot as plt  # type: ignore
        import seaborn as sns  # type: ignore
        import numpy as np  # type: ignore

        sns.set_style("whitegrid")
        plt.switch_backend("Agg")

        df = _read_data_to_pandas(input_file_path)
        
        fig, ax = plt.subplots(figsize=(12, 7))
        if y_col: # Bar plot with aggregation
            estimator_func = {'mean': np.mean, 'sum': np.sum, 'median': np.median}.get(estimator, np.mean)
            sns.barplot(data=df, x=x_col, y=y_col, hue=hue_col, estimator=estimator_func, ax=ax)
            plot_title = title if title else f"{estimator.capitalize()} of {y_col} by {x_col}"
            ax.set_ylabel(f"{estimator.capitalize()} of {y_col}")
        else: # Count plot
            sns.countplot(data=df, x=x_col, hue=hue_col, ax=ax)
            plot_title = title if title else f"Count of {x_col}"
            ax.set_ylabel("Count")

        ax.set_title(plot_title)
        ax.set_xlabel(x_col)
        plt.xticks(rotation=45, ha="right")
        fig.tight_layout()

        filename = f"barplot_{x_col}.png"
        output_path = _save_plot(fig, output_dir, filename)
        return {"status": "success", "image_path": output_path, "description": plot_title}
    except Exception as e:
        return {"error": f"Failed to create bar plot: {str(e)}"}

@register_tool
@tool_card(
    keywords=["plot", "line chart", "time series", "trend"],
    description="Generates a line plot, ideal for showing trends over time or a continuous variable."
)
def create_line_plot(
    input_file_path: Annotated[str, "Path to the input data file (CSV or Parquet)."],
    output_dir: Annotated[str, "Directory to save the plot image."],
    x_col: Annotated[str, "Column for the X-axis (often time or a continuous variable)."],
    y_col: Annotated[str, "Numerical column for the Y-axis."],
    hue_col: Annotated[Optional[str], "Optional categorical column to create separate lines for each category."] = None,
    title: Annotated[Optional[str], "Custom title for the plot."] = None,
) -> Dict[str, Any]:
    """
    Creates a line plot to visualize the trend of a numerical variable over another.
    This is commonly used for time-series data.
    """
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns

        sns.set_style("whitegrid")
        plt.switch_backend("Agg")
        
        df = _read_data_to_pandas(input_file_path)
        df = df.sort_values(by=x_col)

        fig, ax = plt.subplots(figsize=(12, 6))
        sns.lineplot(data=df, x=x_col, y=y_col, hue=hue_col, marker="o", ax=ax)
        
        plot_title = title if title else f"Line Plot of {y_col} over {x_col}"
        ax.set_title(plot_title)
        ax.set_xlabel(x_col)
        ax.set_ylabel(y_col)
        plt.xticks(rotation=45, ha="right")
        fig.tight_layout()

        filename = f"lineplot_{y_col}_over_{x_col}.png"
        output_path = _save_plot(fig, output_dir, filename)
        return {"status": "success", "image_path": output_path, "description": plot_title}
    except Exception as e:
        return {"error": f"Failed to create line plot: {str(e)}"}

@register_tool
@tool_card(
    keywords=["plot", "box plot", "distribution", "quartiles", "outliers"],
    description="Generates a box plot to show the distribution of a numerical variable, potentially grouped by a category."
)
def create_box_plot(
    input_file_path: Annotated[str, "Path to the input data file (CSV or Parquet)."],
    output_dir: Annotated[str, "Directory to save the plot image."],
    y_col: Annotated[str, "The numerical column whose distribution is to be plotted."],
    x_col: Annotated[Optional[str], "Optional categorical column to group the box plots by."] = None,
    hue_col: Annotated[Optional[str], "Optional categorical column for further color grouping."] = None,
    title: Annotated[Optional[str], "Custom title for the plot."] = None,
) -> Dict[str, Any]:
    """
    Creates a box plot to summarize the distribution of a numeric variable.
    It can be a single box plot or multiple plots grouped by one or two categorical variables.
    """
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns

        sns.set_style("whitegrid")
        plt.switch_backend("Agg")

        df = _read_data_to_pandas(input_file_path)
        
        fig, ax = plt.subplots(figsize=(12, 7))
        sns.boxplot(data=df, x=x_col, y=y_col, hue=hue_col, ax=ax)
        
        plot_title = title if title else f"Box Plot of {y_col}" + (f" by {x_col}" if x_col else "")
        ax.set_title(plot_title)
        ax.set_xlabel(x_col if x_col else "")
        ax.set_ylabel(y_col)
        plt.xticks(rotation=45, ha="right")
        fig.tight_layout()

        filename = f"boxplot_{y_col}{'_by_'+x_col if x_col else ''}.png"
        output_path = _save_plot(fig, output_dir, filename)
        return {"status": "success", "image_path": output_path, "description": plot_title}
    except Exception as e:
        return {"error": f"Failed to create box plot: {str(e)}"}

@register_tool
@tool_card(
    keywords=["plot", "heatmap", "correlation matrix"],
    description="Generates a heatmap of the correlation matrix for numerical columns."
)
def create_correlation_heatmap(
    input_file_path: Annotated[str, "Path to the input data file (CSV or Parquet)."],
    output_dir: Annotated[str, "Directory to save the plot image."],
    columns: Annotated[Optional[List[str]], "Specific numerical columns to include. If None, all numerical columns are used."] = None,
    title: Annotated[Optional[str], "Custom title for the plot."] = None,
) -> Dict[str, Any]:
    """
    Calculates the correlation between numerical columns and visualizes it as a heatmap.
    This is useful for identifying multicollinearity and relationships between features.
    """
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        import numpy as np

        sns.set_style("whitegrid")
        plt.switch_backend("Agg")

        df = _read_data_to_pandas(input_file_path)
        
        numeric_df = df.select_dtypes(include=np.number)
        if columns:
            numeric_df = numeric_df[columns]
            
        corr_matrix = numeric_df.corr()
        
        fig, ax = plt.subplots(figsize=(12, 10))
        sns.heatmap(corr_matrix, annot=True, cmap="coolwarm", fmt=".2f", linewidths=0.5, ax=ax)
        
        plot_title = title if title else "Correlation Matrix"
        ax.set_title(plot_title)
        plt.xticks(rotation=45, ha="right")
        plt.yticks(rotation=0)
        fig.tight_layout()

        filename = "correlation_heatmap.png"
        output_path = _save_plot(fig, output_dir, filename)
        return {"status": "success", "image_path": output_path, "description": plot_title}
    except Exception as e:
        return {"error": f"Failed to create heatmap: {str(e)}"} 