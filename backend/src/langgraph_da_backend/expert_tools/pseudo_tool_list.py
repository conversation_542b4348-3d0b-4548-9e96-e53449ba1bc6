import os
import uuid
from typing import Annotated, Any, Dict, List, Optional, Union

import hyperopt as hp
import numpy as np
import pandas as pd
import pingouin as pg
import plotly.express as px
import plotly.figure_factory as ff
import plotly.graph_objects as go
import xgboost as xgb
from hyperopt import STATUS_OK, Trials, fmin, hp, space_eval, tpe
from langgraph_da_backend.utils.static import CODE_OUTPUT_DIR
from langgraph_da_backend.utils.tool.tool_card import register_tool, tool_card
from loguru import logger
from plotly.subplots import make_subplots
from scipy import stats
from scipy.signal import find_peaks
from sklearn.cluster import AffinityPropagation, AgglomerativeClustering, KMeans
from sklearn.ensemble import RandomForestRegressor
from sklearn.exceptions import NotFittedError
from sklearn.linear_model import (
    Lasso,
    LinearRegression,
    LogisticRegression,
    Ridge,
)
from sklearn.metrics import (
    accuracy_score,
    classification_report,
    mean_squared_error,
    r2_score,
)
from sklearn.model_selection import (
    GridSearchCV,
    cross_val_score,
    train_test_split,
)
from sklearn.naive_bayes import GaussianNB
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVC, SVR, LinearSVR
from sklearn.tree import DecisionTreeClassifier
from statsforecast import StatsForecast
from statsforecast.models import AutoARIMA, AutoETS, AutoMFLES
from statsmodels.multivariate.manova import MANOVA
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import acf, adfuller, kpss


def _create_visualization_dict(
    function_name: str, figures: Dict[str, str]
) -> Dict[str, str]:
    """
    Helper function to create visualization dictionary with proper format.

    Args:
        function_name: Name of the function creating the visualization
        figures: Dictionary with figure paths as keys and descriptions as values

    Returns:
        Dictionary with figure paths as keys and formatted descriptions as values
    """
    visualization = {}
    for path, description in figures.items():
        formatted_description = f"[{function_name.upper()}] {description}"
        visualization[path] = formatted_description
    return visualization


@register_tool
@tool_card(keywords=["linear", "lasso", "regression", "numeric", "predict"])
def fit_linear_regression_model(
    data: Annotated[pd.DataFrame, "The input dataframe containing the data."],
    endog_col: Annotated[
        str, "The column name of the Y variable (response variable)"
    ],
    exog_cols: Annotated[
        List[str], "The column names of the X variables (explanatory variables)"
    ],
    model_type: Annotated[
        str,
        "The type of the regression model. Currently only support 'linear' and 'lasso'.",
    ] = "linear",
) -> Dict[str, Any]:
    """
    Fit a linear regression model on the given numeric data. Then computes statistics of p-values for estimating the significance of the coefficients.
    The fitted regression model can later be used to make predictions.
    Note:
        - linear: The ordinary least squares (OLS) regression model, useful for fitting linear relationships.
        - lasso: An L1-regularized regression model, which is useful when there are many irrelevant or noisy variables in the data. The fitted coefficients are shrunk towards zero, which can help to reduce overfitting.
    Returns:
        A dictionary containing the fitted regression model's coefficients and comprehensive fitting statistics.
    """
    model_type = model_type.lower().strip()
    if model_type not in ["linear", "lasso"]:
        return {"error": "Unsupported model type. Use 'linear' or 'lasso'."}

    try:
        X = data[exog_cols]
        y = data[endog_col]

        if type == "linear":
            model = LinearRegression()
        else:  # lasso
            model = Lasso()

        model.fit(X, y)
        y_pred = model.predict(X)

        # Common metrics
        n = len(y)
        p = len(exog_cols)
        r2 = r2_score(y, y_pred)

        # Adjusted R-squared is only meaningful for OLS
        adj_r2 = (
            1 - (1 - r2) * (n - 1) / (n - p - 1) if type == "linear" else None
        )

        mse = mean_squared_error(y, y_pred)

        results = {
            "model_type": type,
            "coefficients": dict(zip(exog_cols, model.coef_)),
            "intercept": model.intercept_,
            "r2": r2,
            "r2_adjusted": adj_r2,
            "mean_squared_error": mse,
            "n_observations": n,
            "df_model": p,
            "df_residuals": n - p - 1,
        }

        if type == "linear":
            # Calculate F-statistic and its p-value for OLS
            f_stat = (r2 / p) / ((1 - r2) / (n - p - 1))
            f_p_value = stats.f.sf(f_stat, p, n - p - 1)
            results["f_statistic"] = f_stat
            results["f_p_value"] = f_p_value

            # Calculate p-values for coefficients for OLS
            X_with_const = np.c_[np.ones(n), X.values]

            # Check for perfect multicollinearity
            if np.linalg.matrix_rank(X_with_const) < X_with_const.shape[1]:
                results["p_values_note"] = (
                    "P-values could not be calculated due to perfect multicollinearity."
                )
            else:
                residuals = y.values - y_pred
                df_resid = n - p - 1
                rss = np.sum(residuals**2)
                s2 = rss / df_resid
                xtx_inv = np.linalg.inv(X_with_const.T @ X_with_const)
                se_b = np.sqrt(np.diag(s2 * xtx_inv))
                all_coefs = np.concatenate(([model.intercept_], model.coef_))
                t_stats = all_coefs / se_b
                p_values = stats.t.sf(np.abs(t_stats), df=df_resid) * 2
                results["p_values"] = dict(
                    zip(["intercept"] + exog_cols, p_values)
                )
        else:  # lasso
            results["note"] = (
                "P-values, F-statistic, and adjusted R-squared are not typically calculated for Lasso regression."
            )

        # Create visualizations
        figures = {}

        # 1. Actual vs Predicted scatter plot
        fig1 = px.scatter(
            x=y.values,
            y=y_pred,
            labels={"x": f"Actual {endog_col}", "y": f"Predicted {endog_col}"},
            title=f"{model_type.title()} Regression: Actual vs Predicted Values",
        )
        # Add perfect prediction line
        min_val, max_val = min(y.min(), y_pred.min()), max(
            y.max(), y_pred.max()
        )
        fig1.add_trace(
            go.Scatter(
                x=[min_val, max_val],
                y=[min_val, max_val],
                mode="lines",
                name="Perfect Prediction",
                line=dict(color="red", dash="dash"),
            )
        )
        fig1_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"linear_regression_actual_vs_predicted_{uuid.uuid4().hex[:8]}.html",
        )
        fig1.write_html(fig1_path)
        figures[fig1_path] = (
            f"Scatter plot showing actual vs predicted values for {endog_col} with R² = {results['r_squared']:.3f}"
        )

        # 2. Residuals plot
        residuals = y.values - y_pred
        fig2 = px.scatter(
            x=y_pred,
            y=residuals,
            labels={"x": f"Predicted {endog_col}", "y": "Residuals"},
            title=f"{model_type.title()} Regression: Residuals vs Fitted Values",
        )
        fig2.add_hline(y=0, line_dash="dash", line_color="red")
        fig2_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"linear_regression_residuals_{uuid.uuid4().hex[:8]}.html",
        )
        fig2.write_html(fig2_path)
        figures[fig2_path] = (
            "Residuals plot to check for homoscedasticity and model assumptions"
        )

        # 3. Feature coefficients bar chart
        if model_type == "linear":
            coef_data = pd.DataFrame(
                {
                    "Feature": exog_cols,
                    "Coefficient": model.coef_,
                    "Abs_Coefficient": np.abs(model.coef_),
                }
            ).sort_values("Abs_Coefficient", ascending=True)

            fig3 = px.bar(
                coef_data,
                x="Coefficient",
                y="Feature",
                orientation="h",
                title=f"Feature Coefficients in {model_type.title()} Regression",
                color="Coefficient",
                color_continuous_scale="RdBu_r",
            )
            fig3_path = os.path.join(
                CODE_OUTPUT_DIR,
                f"linear_regression_coefficients_{uuid.uuid4().hex[:8]}.html",
            )
            fig3.write_html(fig3_path)
            figures[fig3_path] = (
                "Bar chart showing the magnitude and direction of feature coefficients"
            )

        results["visualization"] = _create_visualization_dict(
            "fit_linear_regression_model", figures
        )
        return {"status": "success", "results": results}

    except KeyError as e:
        return {
            "error": f"Column not found: {e}. Ensure all specified columns exist in the data."
        }
    except Exception as e:
        return {"error": f"An error occurred during model fitting: {str(e)}"}


@register_tool
@tool_card(
    keywords=[
        "regression",
        "random-forest",
        "svm",
        "support-vector-machine",
        "svr",
        "predictive",
    ]
)
def fit_advanced_regression_model(
    data: Annotated[pd.DataFrame, "The input pandas DataFrame."],
    endog_col: Annotated[
        str, "The column name of the Y variable (response variable)"
    ],
    exog_cols: Annotated[
        List[str], "The column names of the X variables (explanatory variables)"
    ],
    model_type: Annotated[
        str,
        "The type of the regression model. Currently only support 'linearsvr' and 'xgboost'.",
    ],
    max_evals: Annotated[
        int, "The maximum number of hyperparameter evaluations"
    ] = 50,
):
    """
    Fits a machine learning regression model with hyperparameter tuning and returns the model
    along with its feature importances.

    This function supports two types of models:
    - linearsvr: A Support Vector Regression model with a linear kernel. It's generally
      fast to train and works well for high-dimensional, linearly separable data.
    - xgboost: An implementation of gradient boosted decision trees designed for speed
      and performance. It often provides high accuracy.

    For the 'xgboost' model, this function automatically performs hyperparameter tuning
    using a grid search to find the best model configuration.

    Args:
        data: The input pandas DataFrame.
        endog_col: The column name of the response variable (Y).
        exog_cols: A list of column names for the explanatory variables (X).
        model_type: The type of regression model to fit. Either 'linearsvr' or 'xgboost'.

    Returns:
        A dictionary containing two keys:
        - 'model': The fitted model object (either a scikit-learn Pipeline or an
          XGBoost regressor). This model can be used for predictions.
        - 'feature_importances': A pandas DataFrame with the feature names and their
          corresponding importance scores, sorted in descending order.

    Raises:
        TypeError: If the input `data` is not a pandas DataFrame.
        ValueError: If specified columns are not in the DataFrame or if an
                    unsupported model_type is provided.
    """
    # --- Input Validation ---
    if not isinstance(data, pd.DataFrame):
        raise TypeError("The 'data' argument must be a pandas DataFrame.")
    if endog_col not in data.columns:
        raise ValueError(
            f"Endogenous column '{endog_col}' not found in the DataFrame."
        )

    missing_cols = [col for col in exog_cols if col not in data.columns]
    if missing_cols:
        raise ValueError(
            f"Exogenous columns not found in DataFrame: {missing_cols}"
        )

    # --- Data Preparation ---
    X = data[exog_cols]
    y = data[endog_col]

    fitted_model = None
    feature_importance_df = None

    # --- Model Selection and Fitting ---
    if model_type == "linearsvr":
        print("Fitting LinearSVR model...")
        pipeline = Pipeline(
            [
                ("scaler", StandardScaler()),
                (
                    "svr",
                    LinearSVR(
                        random_state=42, dual="auto", tol=1e-5, max_iter=2000
                    ),
                ),
            ]
        )

        pipeline.fit(X, y)
        fitted_model = pipeline

        importances = fitted_model.named_steps["svr"].coef_
        feature_importance_df = (
            pd.DataFrame({"feature": exog_cols, "importance": importances})
            .sort_values(by="importance", key=abs, ascending=False)
            .reset_index(drop=True)
        )

    elif model_type == "xgboost":
        print(
            f"Fitting XGBoost model with Bayesian Optimization ({max_evals} evals)..."
        )

        # 1. Define the hyperparameter search space for hyperopt
        space = {
            "n_estimators": hp.quniform("n_estimators", 50, 300, 50),
            "max_depth": hp.quniform("max_depth", 3, 10, 1),
            "learning_rate": hp.loguniform(
                "learning_rate", np.log(0.01), np.log(0.3)
            ),
            "subsample": hp.uniform("subsample", 0.5, 1.0),
            "colsample_bytree": hp.uniform("colsample_bytree", 0.5, 1),
            "gamma": hp.uniform("gamma", 1, 9),
            "reg_alpha": hp.quniform("reg_alpha", 40, 180, 1),
            "reg_lambda": hp.uniform("reg_lambda", 0, 1),
            "seed": 42,
        }

        # 2. Define the objective function for hyperopt to minimize
        def objective(params):
            """Objective function for hyperopt to minimize."""
            # hyperopt sends float values for integer parameters, so we must cast them
            params["max_depth"] = int(params["max_depth"])
            params["n_estimators"] = int(params["n_estimators"])

            model = xgb.XGBRegressor(
                objective="reg:squarederror", random_state=42, **params
            )

            # We use cross-validation to get a robust estimate of the model's performance.
            # We want to minimize the negative MSE, which is equivalent to maximizing MSE.
            score = cross_val_score(
                model, X, y, scoring="neg_mean_squared_error", cv=5
            ).mean()

            # hyperopt minimizes the 'loss', so we return the negative of the score.
            loss = -score

            return {"loss": loss, "status": STATUS_OK, "params": params}

        # 3. Run the Bayesian Optimization
        trials = Trials()
        best_hyperparams = fmin(
            fn=objective,
            space=space,
            algo=tpe.suggest,
            max_evals=max_evals,
            trials=trials,
            rstate=np.random.default_rng(42),  # for reproducibility
        )

        # space_eval converts the index-based best_hyperparams dictionary
        # to the actual parameter values.
        best_params = space_eval(space, best_hyperparams)
        print(f"Best parameters found: {best_params}")

        # 4. Train the final model with the best hyperparameters on the full dataset
        print("Training final model with best parameters...")
        best_params["max_depth"] = int(best_params["max_depth"])
        best_params["n_estimators"] = int(best_params["n_estimators"])

        fitted_model = xgb.XGBRegressor(
            objective="reg:squarederror", random_state=42, **best_params
        )
        fitted_model.fit(X, y)

        # Get feature importances from the final model
        importances = fitted_model.feature_importances_
        feature_importance_df = (
            pd.DataFrame({"feature": exog_cols, "importance": importances})
            .sort_values(by="importance", ascending=False)
            .reset_index(drop=True)
        )

    else:
        raise ValueError(
            f"Unsupported model_type: '{model_type}'. Supported types are 'linearsvr' and 'xgboost'."
        )

    # --- Create Visualizations ---
    figures = {}

    # 1. Feature importance plot
    fig1 = px.bar(
        feature_importance_df.head(15),  # Top 15 features
        x="importance",
        y="feature",
        orientation="h",
        title=f"{model_type.title()} Model: Feature Importance",
        labels={"importance": "Importance Score", "feature": "Features"},
    )
    fig1.update_layout(yaxis={"categoryorder": "total ascending"})
    fig1_path = os.path.join(
        CODE_OUTPUT_DIR,
        f"advanced_regression_feature_importance_{uuid.uuid4().hex[:8]}.html",
    )
    fig1.write_html(fig1_path)
    figures[fig1_path] = (
        f"Feature importance ranking from {model_type} model showing top contributing variables"
    )

    # 2. Actual vs Predicted plot
    y_pred = fitted_model.predict(X)
    fig2 = px.scatter(
        x=y.values,
        y=y_pred,
        labels={"x": f"Actual {endog_col}", "y": f"Predicted {endog_col}"},
        title=f"{model_type.title()} Model: Actual vs Predicted Values",
    )
    # Add perfect prediction line
    min_val, max_val = min(y.min(), y_pred.min()), max(y.max(), y_pred.max())
    fig2.add_trace(
        go.Scatter(
            x=[min_val, max_val],
            y=[min_val, max_val],
            mode="lines",
            name="Perfect Prediction",
            line=dict(color="red", dash="dash"),
        )
    )
    # Calculate R²
    r2 = r2_score(y, y_pred)
    fig2_path = os.path.join(
        CODE_OUTPUT_DIR,
        f"advanced_regression_actual_vs_predicted_{uuid.uuid4().hex[:8]}.html",
    )
    fig2.write_html(fig2_path)
    figures[fig2_path] = (
        f"Scatter plot comparing actual vs predicted values with R² = {r2:.3f}"
    )

    # 3. Residuals distribution
    residuals = y.values - y_pred
    fig3 = px.histogram(
        x=residuals,
        nbins=30,
        title=f"{model_type.title()} Model: Residuals Distribution",
        labels={"x": "Residuals", "y": "Frequency"},
    )
    fig3.add_vline(x=0, line_dash="dash", line_color="red")
    fig3_path = os.path.join(
        CODE_OUTPUT_DIR,
        f"advanced_regression_residuals_dist_{uuid.uuid4().hex[:8]}.html",
    )
    fig3.write_html(fig3_path)
    figures[fig3_path] = (
        "Histogram of residuals to assess model fit and normality assumptions"
    )

    # --- Return Results ---
    return {
        "model": fitted_model,
        "feature_importances": feature_importance_df,
        "visualization": _create_visualization_dict(
            "fit_advanced_regression_model", figures
        ),
    }


@register_tool
@tool_card(keywords=["ANOVA", "statistical-test", "diagnostic", "t-test"])
def run_statistical_test(
    data: Annotated[pd.DataFrame, "The input pandas DataFrame."],
    y_cols: Annotated[
        List[str], "A list of dependent (Y) variable column names."
    ],
    x_col: Annotated[
        str, "The column name of the independent (X) grouping variable."
    ],
    p_value_threshold: Annotated[
        float, "The significance level (alpha)."
    ] = 0.05,
) -> Dict[str, Any]:
    """
    Performs statistical tests to determine if an independent variable (X) has a
    significant effect on one or more dependent variables (Y).

    The function automatically performs the following steps:
    1.  Validates the input data and parameters.
    2.  Checks the assumptions of normality (Shapiro-Wilk test for each group) and
        homogeneity of variances (Levene's test) for each dependent variable.
    3.  Based on the number of groups in X and the number of Y variables, and
        only if assumptions are met, it selects and runs the appropriate test:
        - 2 groups in X, 1 Y variable: Independent samples t-test.
        - >2 groups in X, 1 Y variable: One-way ANOVA.
        - Any number of groups in X, >1 Y variable: MANOVA.
    4.  Returns a detailed dictionary with assumption checks, test results, and a summary.

    Args:
        data: The input pandas DataFrame.
        y_cols: A list of the dependent variable column names (must be numeric).
        x_col: The column name for the independent, categorical grouping variable.
        p_value_threshold: The significance level (alpha) for all tests.

    Returns:
        A dictionary containing a comprehensive report of the analysis, including:
        - 'summary': A human-readable summary of the findings.
        - 'assumptions': Results from normality and homogeneity tests.
        - 'assumptions_met': A boolean indicating if all assumptions passed.
        - 'test_performed': The name of the main statistical test run (or None).
        - 'test_results': The output from the statistical test.

    Raises:
        ValueError: For invalid input parameters or data types.
    """
    # --- 1. Input Validation and Setup ---
    if not isinstance(data, pd.DataFrame):
        raise TypeError("The 'data' argument must be a pandas DataFrame.")
    if x_col not in data.columns:
        raise ValueError(
            f"Grouping column '{x_col}' not found in the DataFrame."
        )
    for y_col in y_cols:
        if y_col not in data.columns:
            raise ValueError(f"Dependent variable column '{y_col}' not found.")
        if not pd.api.types.is_numeric_dtype(data[y_col]):
            raise ValueError(
                f"Dependent variable column '{y_col}' must be numeric."
            )

    groups = data[x_col].unique()
    num_groups = len(groups)
    if num_groups < 2:
        raise ValueError(
            f"Grouping column '{x_col}' must have at least two unique groups."
        )

    results: Dict[str, Any] = {
        "summary": "",
        "assumptions": {"normality": {}, "homogeneity": {}},
        "assumptions_met": True,
        "test_performed": None,
        "test_results": None,
    }

    # --- 2. Assumption Checking ---
    print("--- Checking Assumptions ---")
    summary_lines = []

    for y_col in y_cols:
        # Prepare data for tests: a list of series, one for each group
        grouped_data = [data[y_col][data[x_col] == g] for g in groups]

        # a) Normality Test (Shapiro-Wilk for each group)
        normality_p_values = [
            stats.shapiro(g_data).pvalue for g_data in grouped_data
        ]
        results["assumptions"]["normality"][y_col] = {
            group: p for group, p in zip(groups, normality_p_values)
        }

        for group, p in zip(groups, normality_p_values):
            if p < p_value_threshold:
                results["assumptions_met"] = False
                summary_lines.append(
                    f"WARNING: Normality assumption failed for '{y_col}' in group '{group}' (p={p:.3f})."
                )

        # b) Homogeneity of Variances Test (Levene's Test)
        _, levene_p = stats.levene(*grouped_data)
        results["assumptions"]["homogeneity"][y_col] = {"p_value": levene_p}

        if levene_p < p_value_threshold:
            results["assumptions_met"] = False
            summary_lines.append(
                f"WARNING: Homogeneity of variance assumption failed for '{y_col}' (p={levene_p:.3f})."
            )

    if not summary_lines:
        summary_lines.append(
            "SUCCESS: All assumptions of normality and homogeneity of variance were met."
        )

    print("\n".join(summary_lines))

    if not results["assumptions_met"]:
        results["summary"] = (
            "\n".join(summary_lines) + "\nCannot proceed with parametric tests."
        )
        print(
            "Cannot proceed with parametric tests due to assumption violations."
        )
        return results

    # --- 3. Perform Appropriate Statistical Test ---
    print("\n--- Performing Main Statistical Test ---")

    # Case A: T-Test (1 Y, 2 Groups)
    if len(y_cols) == 1 and num_groups == 2:
        y_col = y_cols[0]
        test_name = "Independent Samples T-Test"
        results["test_performed"] = test_name

        group1_data = data[y_col][data[x_col] == groups[0]]
        group2_data = data[y_col][data[x_col] == groups[1]]

        t_stat, p_val = stats.ttest_ind(group1_data, group2_data)
        results["test_results"] = {"statistic": t_stat, "p_value": p_val}

        summary_lines.append(f"{test_name} was performed on '{y_col}'.")
        if p_val < p_value_threshold:
            summary_lines.append(
                f"Result: A significant difference was found between the groups (p={p_val:.3f})."
            )
        else:
            summary_lines.append(
                f"Result: No significant difference was found between the groups (p={p_val:.3f})."
            )

    # Case B: ANOVA (1 Y, >2 Groups)
    elif len(y_cols) == 1 and num_groups > 2:
        y_col = y_cols[0]
        test_name = "One-Way ANOVA"
        results["test_performed"] = test_name

        aov = pg.anova(data=data, dv=y_col, between=x_col, detailed=True)
        results["test_results"] = aov.to_dict("records")
        p_val = aov.loc[0, "p-GG-corr"]  # Greenhouse-Geisser corrected p-value

        summary_lines.append(f"{test_name} was performed on '{y_col}'.")
        if p_val < p_value_threshold:
            summary_lines.append(
                f"Result: A significant difference was found between at least two groups (p={p_val:.3f})."
            )
        else:
            summary_lines.append(
                f"Result: No significant difference was found between the groups (p={p_val:.3f})."
            )

    # Case C: MANOVA (>1 Y, Any Groups)
    elif len(y_cols) > 1:
        test_name = "MANOVA"
        results["test_performed"] = test_name

        formula = f"{' + '.join(y_cols)} ~ {x_col}"
        maov = MANOVA.from_formula(formula, data=data)
        mv_test = maov.mv_test()
        results["test_results"] = mv_test.results[x_col]["stat"].to_dict()
        p_val = mv_test.results[x_col]["stat"].loc["Wilks' lambda", "Pr > F"]

        summary_lines.append(f"{test_name} was performed.")
        if p_val < p_value_threshold:
            summary_lines.append(
                f"Result: A significant effect of '{x_col}' was found on the combined dependent variables (Wilks' Lambda p={p_val:.3f})."
            )
        else:
            summary_lines.append(
                f"Result: No significant effect of '{x_col}' was found on the combined dependent variables (Wilks' Lambda p={p_val:.3f})."
            )

    else:
        summary_lines.append(
            "No appropriate statistical test was found for the given data structure."
        )

    results["summary"] = "\n".join(summary_lines)
    print(f"\nFinal Summary:\n{results['summary']}")

    # Create visualizations
    figures = {}

    # 1. Box plots for each Y variable by X groups
    for y_col in y_cols:
        fig = px.box(
            data,
            x=x_col,
            y=y_col,
            title=f"Distribution of {y_col} by {x_col}",
            labels={x_col: x_col, y_col: y_col},
        )
        fig.update_layout(showlegend=False)
        fig_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"statistical_test_boxplot_{y_col}_{uuid.uuid4().hex[:8]}.html",
        )
        fig.write_html(fig_path)
        figures[fig_path] = (
            f"Box plot showing distribution of {y_col} across different {x_col} groups"
        )

    # 2. Summary statistics heatmap
    if len(y_cols) > 1:
        summary_stats = (
            data.groupby(x_col)[y_cols].agg(["mean", "std"]).round(3)
        )
        # Flatten column names
        summary_stats.columns = [
            f"{col}_{stat}" for col, stat in summary_stats.columns
        ]

        fig2 = px.imshow(
            summary_stats.T,
            labels=dict(x=x_col, y="Variable_Statistic", color="Value"),
            title=f"Summary Statistics Heatmap by {x_col}",
            aspect="auto",
        )
        fig2_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"statistical_test_heatmap_{uuid.uuid4().hex[:8]}.html",
        )
        fig2.write_html(fig2_path)
        figures[fig2_path] = (
            f"Heatmap of mean and standard deviation for all variables by {x_col} groups"
        )

    # 3. P-values visualization if available
    if "test_results" in results and isinstance(results["test_results"], dict):
        p_values_data = []
        for key, value in results["test_results"].items():
            if "p_value" in str(key).lower() or "pr > f" in str(key).lower():
                p_values_data.append({"Test": key, "P_Value": value})

        if p_values_data:
            p_df = pd.DataFrame(p_values_data)
            fig3 = px.bar(
                p_df,
                x="Test",
                y="P_Value",
                title="Statistical Test P-Values",
                labels={"P_Value": "P-Value", "Test": "Test Type"},
            )
            fig3.add_hline(
                y=p_value_threshold,
                line_dash="dash",
                line_color="red",
                annotation_text=f"α = {p_value_threshold}",
            )
            fig3_path = os.path.join(
                CODE_OUTPUT_DIR,
                f"statistical_test_pvalues_{uuid.uuid4().hex[:8]}.html",
            )
            fig3.write_html(fig3_path)
            figures[fig3_path] = (
                f"Bar chart of p-values from statistical tests with significance threshold α = {p_value_threshold}"
            )

    results["visualization"] = _create_visualization_dict(
        "run_statistical_test", figures
    )
    return results


@register_tool
@tool_card(
    keywords=[
        "classification",
        "logistic-regression",
        "naive-bayes",
        "decision-tree",
    ]
)
def fit_classification_model(
    data: Annotated[pd.DataFrame, "The input pandas DataFrame."],
    endog_col: Annotated[
        str, "The column name of the Y variable (response variable)"
    ],
    exog_cols: Annotated[
        List[str], "The column names of the X variables (explanatory variables)"
    ],
    model_type: Annotated[
        str,
        "The type of the classification model. Supports 'logistic', 'naive-bayes', and 'decision-tree'.",
    ],
) -> Dict[str, Any]:
    """
    Fits a classification model and provides insights into the factors that
    contribute to the classification result.

    --- Model Options ---

    'logistic': Logistic Regression
        - How it works: A linear model that estimates the probability of a
          particular outcome. It finds the best-fitting line (or plane) that
          separates the different classes.
        - Pros: Highly interpretable. The model's coefficients directly tell you
          the direction (positive/negative) and strength of each feature's
          influence on the outcome. It's fast and works well when the relationship
          between features and the outcome is roughly linear.
        - Cons: Assumes a linear relationship between features and the log-odds
          of the outcome. May not perform well on complex problems with non-linear
          patterns.

    'naive-bayes': Naive Bayes (Gaussian)
        - How it works: A probabilistic algorithm that uses Bayes' theorem. It
          calculates the probability of a data point belonging to each class
          based on its features, with the "naive" assumption that all features
          are independent of each other.
        - Pros: Very fast to train, even on large datasets. Performs surprisingly
          well in many real-world scenarios, especially in text classification
          (e.g., spam filtering). Requires fewer training examples.
        - Cons: The core assumption of feature independence is often violated in
          reality, which can lead to poorer performance if features are highly
          correlated. The direct "importance" of a feature is less intuitive to
          interpret compared to other models.

    'decision-tree': Decision Tree
        - How it works: Creates a tree-like model of decisions. It splits the data
          into smaller and smaller subsets based on feature values, learning simple
          decision rules. The final prediction is based on the majority class in
          the leaf node where a sample ends up.
        - Pros: Very easy to understand and visualize. The decision rules are
          explicit. It can handle non-linear relationships between features and
          the outcome without requiring data transformation.
        - Cons: Prone to overfitting, meaning it might learn the training data too
          well and not generalize to new data. A single tree can be unstable; small
          changes in the data can lead to a completely different tree.

    Args:
        data: The input pandas DataFrame.
        endog_col: The column name of the categorical response variable (Y).
        exog_cols: A list of column names for the explanatory variables (X).
        model_type: The type of classification model to fit.

    Returns:
        A dictionary containing two keys:
        - 'model': The fitted model object (a scikit-learn Pipeline).
        - 'interpretable_statistics': Insights from the model. The format depends
          on the model type (e.g., a DataFrame of coefficients for logistic,
          feature importances for decision tree).
    """
    # --- 1. Input Validation and Setup ---
    if not isinstance(data, pd.DataFrame):
        raise TypeError("The 'data' argument must be a pandas DataFrame.")
    if endog_col not in data.columns:
        raise ValueError(f"Endogenous column '{endog_col}' not found.")
    for col in exog_cols:
        if col not in data.columns:
            raise ValueError(f"Exogenous column '{col}' not found.")
        if not pd.api.types.is_numeric_dtype(data[col]):
            raise ValueError(f"Explanatory column '{col}' must be numeric.")

    X = data[exog_cols]
    y = data[endog_col]
    class_names = np.unique(y)

    fitted_model = None
    interpretable_stats: Any = None

    # --- 2. Model Selection and Fitting ---
    if model_type == "logistic":
        print("Fitting Logistic Regression model...")
        # A pipeline is used to scale data before fitting, which is good practice.
        model = Pipeline(
            [
                ("scaler", StandardScaler()),
                (
                    "classifier",
                    LogisticRegression(random_state=42, multi_class="auto"),
                ),
            ]
        )
        model.fit(X, y)
        fitted_model = model

        # Extract coefficients for interpretation.
        coefficients = model.named_steps["classifier"].coef_

        if len(class_names) <= 2:  # Binary classification
            interpretable_stats = (
                pd.DataFrame(
                    {"feature": exog_cols, "coefficient": coefficients[0]}
                )
                .sort_values(by="coefficient", key=abs, ascending=False)
                .reset_index(drop=True)
            )
        else:  # Multi-class classification
            stats_df = pd.DataFrame({"feature": exog_cols})
            for i, class_name in enumerate(class_names):
                stats_df[f"coefficient_for_{class_name}"] = coefficients[i]
            interpretable_stats = stats_df

    elif model_type == "naive-bayes":
        print("Fitting Gaussian Naive Bayes model...")
        model = Pipeline(
            [("scaler", StandardScaler()), ("classifier", GaussianNB())]
        )
        model.fit(X, y)
        fitted_model = model

        # For Naive Bayes, insights come from the learned class distributions.
        nb_classifier = model.named_steps["classifier"]
        interpretable_stats = {
            "class_priors": {
                class_names[i]: p
                for i, p in enumerate(nb_classifier.class_prior_)
            },
            "feature_means_per_class": pd.DataFrame(
                nb_classifier.theta_, columns=exog_cols, index=class_names
            ),
            "feature_variance_per_class": pd.DataFrame(
                nb_classifier.var_, columns=exog_cols, index=class_names
            ),
        }

    elif model_type == "decision-tree":
        print("Fitting Decision Tree model...")
        # Note: Scaling is not necessary for Decision Trees, but we fit directly.
        model = DecisionTreeClassifier(random_state=42)
        model.fit(X, y)
        fitted_model = model

        # Feature importances are the primary interpretability tool for trees.
        importances = model.feature_importances_
        interpretable_stats = (
            pd.DataFrame({"feature": exog_cols, "importance": importances})
            .sort_values(by="importance", ascending=False)
            .reset_index(drop=True)
        )

    else:
        raise ValueError(
            f"Unsupported model_type: '{model_type}'. Supported types are 'logistic', 'naive-bayes', and 'decision-tree'."
        )

    # --- 3. Create Visualizations ---
    figures = {}

    # Make predictions for visualization
    y_pred = fitted_model.predict(X)
    y_pred_proba = (
        fitted_model.predict_proba(X)
        if hasattr(fitted_model, "predict_proba")
        else None
    )

    # 1. Confusion Matrix
    from sklearn.metrics import confusion_matrix

    cm = confusion_matrix(y, y_pred)
    fig1 = px.imshow(
        cm,
        text_auto=True,
        aspect="auto",
        labels=dict(x="Predicted", y="Actual", color="Count"),
        title=f"{model_type.title()} Classification: Confusion Matrix",
        x=class_names,
        y=class_names,
    )
    fig1_path = os.path.join(
        CODE_OUTPUT_DIR,
        f"classification_confusion_matrix_{uuid.uuid4().hex[:8]}.html",
    )
    fig1.write_html(fig1_path)
    figures[fig1_path] = (
        f"Confusion matrix showing classification performance for {model_type} model"
    )

    # 2. Feature importance/coefficients visualization
    if model_type == "logistic":
        # Logistic regression coefficients
        coef_data = pd.DataFrame(
            {
                "Feature": exog_cols,
                "Coefficient": fitted_model.named_steps["classifier"].coef_[0],
                "Abs_Coefficient": np.abs(
                    fitted_model.named_steps["classifier"].coef_[0]
                ),
            }
        ).sort_values("Abs_Coefficient", ascending=True)

        fig2 = px.bar(
            coef_data,
            x="Coefficient",
            y="Feature",
            orientation="h",
            title=f"{model_type.title()} Classification: Feature Coefficients",
            color="Coefficient",
            color_continuous_scale="RdBu_r",
        )
        fig2_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"classification_coefficients_{uuid.uuid4().hex[:8]}.html",
        )
        fig2.write_html(fig2_path)
        figures[fig2_path] = (
            "Bar chart showing logistic regression coefficients and their impact on classification"
        )

    elif model_type == "decision-tree":
        # Decision tree feature importance
        fig2 = px.bar(
            interpretable_stats.head(10),
            x="importance",
            y="feature",
            orientation="h",
            title=f"{model_type.title()} Classification: Feature Importance",
            labels={"importance": "Importance Score", "feature": "Features"},
        )
        fig2.update_layout(yaxis={"categoryorder": "total ascending"})
        fig2_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"classification_feature_importance_{uuid.uuid4().hex[:8]}.html",
        )
        fig2.write_html(fig2_path)
        figures[fig2_path] = (
            "Feature importance ranking from decision tree showing most influential variables"
        )

    # 3. Class distribution
    class_counts = pd.Series(y).value_counts()
    fig3 = px.pie(
        values=class_counts.values,
        names=[class_names[i] for i in class_counts.index],
        title=f"Class Distribution in {endog_col}",
    )
    fig3_path = os.path.join(
        CODE_OUTPUT_DIR,
        f"classification_class_distribution_{uuid.uuid4().hex[:8]}.html",
    )
    fig3.write_html(fig3_path)
    figures[fig3_path] = (
        f"Pie chart showing the distribution of classes in the target variable {endog_col}"
    )

    # 4. Prediction probabilities (if available)
    if y_pred_proba is not None and len(class_names) == 2:
        # For binary classification, show probability distribution
        prob_df = pd.DataFrame(
            {
                "Probability": y_pred_proba[:, 1],
                "Actual_Class": [class_names[i] for i in y],
                "Predicted_Class": [class_names[i] for i in y_pred],
            }
        )

        fig4 = px.histogram(
            prob_df,
            x="Probability",
            color="Actual_Class",
            title=f"{model_type.title()} Classification: Prediction Probability Distribution",
            labels={
                "Probability": f"Probability of {class_names[1]}",
                "count": "Frequency",
            },
            marginal="box",
        )
        fig4_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"classification_prob_distribution_{uuid.uuid4().hex[:8]}.html",
        )
        fig4.write_html(fig4_path)
        figures[fig4_path] = (
            f"Distribution of prediction probabilities colored by actual class labels"
        )

    # --- 3. Return Results ---
    return {
        "model": fitted_model,
        "interpretable_statistics": interpretable_stats,
        "visualization": _create_visualization_dict(
            "fit_classification_model", figures
        ),
    }


@register_tool
@tool_card(keywords=["ts", "time-series", "forecast", "stat"])
def fit_stats_forecast_model(
    df: Annotated[
        pd.DataFrame, "The input time-series data in a pandas DataFrame."
    ],
    value_cols: Annotated[
        List[str], "The column name(s) of the target variable(s) to forecast."
    ],
    ts_col: Annotated[
        str,
        "The column name of the timestamps. This column should be of a datetime type.",
    ],
    h: Annotated[
        int,
        "The forecast horizon (number of steps to predict into the future).",
    ],
    season_length: Annotated[
        Optional[int],
        "The length of the seasonal period (e.g., 7 for daily data with weekly seasonality).",
    ] = None,
    id_col: Annotated[
        Optional[str],
        "The column name of the identifier variable. If None, assumes a single time-series.",
    ] = None,
    level: Annotated[
        List[int], "The confidence levels for the prediction intervals."
    ] = [95],
    model_selection: Annotated[
        List[str],
        "The list of models to try. Options: 'AutoARIMA', 'AutoETS', 'AutoMFLES'.",
    ] = ["AutoARIMA", "AutoETS", "AutoMFLES"],
) -> Dict[str, Any]:
    """
    Fits statistical forecasting models, selects the best one, and provides deep insights.

    This function leverages `statsforecast` to efficiently fit and compare powerful automated
    time-series forecasting models. It uses cross-validation to robustly evaluate and select
    the best model for each series based on Mean Absolute Error (MAE). Anomalies in the
    historical data are detected using the residuals from the best-performing model.

    Model Explanations:
    -----------------------------------------
    - AutoARIMA:
        useful when the time-series exhibits clear dependence on its own past values
        and past forecast errors, i.e., auto-correlated series.

    - AutoETS:
        useful when the time series has a clear and explicit underlying structure,
        such as a discernible trend and/or seasonality.

    - AutoMFLES:
        useful for complex time series that may not be well-captured by traditional
        statistical models, such as multiple seasonalities, non-linear trends, changepoints.

    Parameters
    ----------
    df : pd.DataFrame
        The input DataFrame, with one row per timestamp and series.
    value_cols : List[str]
        The column name(s) of the target variable(s) to forecast.
    ts_col : str
        The name of the column containing timestamps.
    h : int
        The forecast horizon (number of future steps to predict).
    season_length : Optional[int], default=None
        The main seasonal period of the data.
    id_col : Optional[str], default=None
        The column identifying individual time series.
    level : List[int], default=[95]
        A list of confidence levels for the prediction intervals.
    model_selection : List[str], default=['AutoARIMA', 'AutoETS', 'AutoMFLES']
        A list of model names to fit and compare.

    Returns
    -------
    Dict[str, Any]
        A dictionary containing four key-value pairs:
        - **'forecast_df'**: A DataFrame containing the original data merged with the
          forecasts and confidence intervals from all fitted models.
        - **'performance_metrics'**: A DataFrame from cross-validation showing the
          performance (e.g., MAE, RMSE) of each model on each time series.
        - **'model_insights'**: A DataFrame summarizing the best model chosen for each
          series and its fitted parameters.
        - **'plots'**: A dictionary of interactive Plotly figures showing the forecast
          for each series, keyed by (series_id, variable).
    """
    all_forecasts, all_metrics, all_insights, plots_dict = [], [], [], {}

    for col in value_cols:
        df_prep = df.rename(columns={ts_col: "ds", col: "y"})
        id_col_internal = id_col if id_col else "unique_id"
        if not id_col:
            df_prep[id_col_internal] = "Overall"
        else:
            df_prep = df_prep.rename(columns={id_col: id_col_internal})

        df_prep = df_prep[[id_col_internal, "ds", "y"]].dropna()
        df_prep["ds"] = pd.to_datetime(df_prep["ds"])
        if df_prep.empty:
            continue

        model_map = {
            "AutoARIMA": AutoARIMA(season_length=season_length),
            "AutoETS": AutoETS(season_length=season_length),
            "AutoMFLES": AutoMFLES(
                season_length=season_length, test_size=season_length
            ),
        }
        models_to_run = [
            model_map[m] for m in model_selection if m in model_map
        ]
        if not models_to_run:
            raise ValueError("No valid models selected.")

        sf = StatsForecast(
            models=models_to_run,
            freq=pd.infer_freq(df_prep["ds"].iloc[:10]),
            n_jobs=-1,
        )

        # 1. Cross-validation to find the best model
        cv_df = sf.cross_validation(df=df_prep, h=h, step_size=h, n_windows=3)

        metrics_list = []
        for model_name in model_selection:
            if model_name in cv_df.columns:
                metrics = (
                    cv_df.groupby(id_col_internal)
                    .apply(
                        lambda g: pd.Series(
                            {
                                "mae": np.mean(np.abs(g["y"] - g[model_name])),
                                "rmse": np.sqrt(
                                    np.mean((g["y"] - g[model_name]) ** 2)
                                ),
                            }
                        )
                    )
                    .reset_index()
                )
                metrics["model"] = model_name
                metrics_list.append(metrics)

        perf_metrics = (
            pd.concat(metrics_list, ignore_index=True)
            if metrics_list
            else pd.DataFrame()
        )
        perf_metrics["variable"] = col
        all_metrics.append(perf_metrics)

        # 2. Forecast with fitted values to get in-sample predictions
        # This is the main change: using fitted=True
        forecast_df = sf.forecast(df=df_prep, h=h, level=level, fitted=True)
        insample_forecasts = sf.forecast_fitted_values()

        full_df = pd.merge(
            df_prep, forecast_df, on=[id_col_internal, "ds"], how="outer"
        )
        full_df["variable"] = col
        all_forecasts.append(full_df)

        model_params = sf.get_model_params()
        best_models = perf_metrics.loc[
            perf_metrics.groupby(id_col_internal)["mae"].idxmin()
        ]

        for _, row in best_models.iterrows():
            uid, best_model_name = row[id_col_internal], row["model"]
            params = model_params.loc[uid, best_model_name]
            all_insights.append(
                {
                    "unique_id": uid,
                    "variable": col,
                    "best_model": best_model_name,
                    "parameters": str(params),
                }
            )

            # 3. Anomaly Detection using in-sample prediction intervals
            insample_series = insample_forecasts[
                insample_forecasts[id_col_internal] == uid
            ]

            # Find the correct lo/hi columns for the best model and chosen level
            level_to_check = level[
                0
            ]  # Use the first level provided for anomaly detection
            lo_col = f"{best_model_name}-lo-{level_to_check}"
            hi_col = f"{best_model_name}-hi-{level_to_check}"

            anomalies = insample_series[
                ~insample_series["y"].between(
                    insample_series[lo_col], insample_series[hi_col]
                )
            ]
            anomaly_dates = anomalies["ds"]

            # 4. Generate plot for this series
            plot_df = full_df[full_df[id_col_internal] == uid]
            metrics_for_plot = perf_metrics[
                perf_metrics[id_col_internal] == uid
            ].set_index([id_col_internal, "model"])
            fig = _plot_forecast(
                plot_df, metrics_for_plot, anomaly_dates, uid, col, h
            )
            if fig:
                plots_dict[(uid, col)] = fig

    # Create additional visualizations
    figures = {}

    # 1. Model performance comparison
    if all_metrics:
        combined_metrics = pd.concat(all_metrics, ignore_index=True)
        if not combined_metrics.empty:
            fig1 = px.bar(
                combined_metrics,
                x="model",
                y="mae",
                color="variable",
                title="Model Performance Comparison (MAE)",
                labels={"mae": "Mean Absolute Error", "model": "Model Type"},
            )
            fig1_path = os.path.join(
                CODE_OUTPUT_DIR,
                f"forecast_model_performance_{uuid.uuid4().hex[:8]}.html",
            )
            fig1.write_html(fig1_path)
            figures[fig1_path] = (
                "Bar chart comparing Mean Absolute Error (MAE) across different forecasting models"
            )

    # 2. Forecast accuracy by horizon
    if all_forecasts:
        combined_forecasts = pd.concat(all_forecasts, ignore_index=True)
        if not combined_forecasts.empty and "y" in combined_forecasts.columns:
            # Calculate errors for each forecast horizon
            forecast_subset = combined_forecasts[
                combined_forecasts["y"].notna()
            ]
            if not forecast_subset.empty:
                for model_name in model_selection:
                    if model_name in forecast_subset.columns:
                        forecast_subset[f"{model_name}_error"] = np.abs(
                            forecast_subset["y"] - forecast_subset[model_name]
                        )

                # Create horizon analysis
                forecast_subset["horizon"] = (
                    forecast_subset.groupby(
                        ["variable", id_col_internal]
                    ).cumcount()
                    + 1
                )
                horizon_errors = []
                for model_name in model_selection:
                    if f"{model_name}_error" in forecast_subset.columns:
                        horizon_error = (
                            forecast_subset.groupby("horizon")[
                                f"{model_name}_error"
                            ]
                            .mean()
                            .reset_index()
                        )
                        horizon_error["model"] = model_name
                        horizon_errors.append(horizon_error)

                if horizon_errors:
                    horizon_df = pd.concat(horizon_errors, ignore_index=True)
                    fig2 = px.line(
                        horizon_df,
                        x="horizon",
                        y=f"{model_selection[0]}_error",
                        color="model",
                        title="Forecast Error by Horizon",
                        labels={
                            "horizon": "Forecast Horizon",
                            f"{model_selection[0]}_error": "Mean Absolute Error",
                        },
                    )
                    fig2_path = os.path.join(
                        CODE_OUTPUT_DIR,
                        f"forecast_horizon_analysis_{uuid.uuid4().hex[:8]}.html",
                    )
                    fig2.write_html(fig2_path)
                    figures[fig2_path] = (
                        "Line chart showing how forecast accuracy changes with prediction horizon"
                    )

    # 3. Save existing plots from plots_dict
    for (uid, col), fig in plots_dict.items():
        fig_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"forecast_timeseries_{uid}_{col}_{uuid.uuid4().hex[:8]}.html",
        )
        fig.write_html(fig_path)
        figures[fig_path] = (
            f"Time series forecast plot for {col} (series: {uid}) showing historical data, predictions, and confidence intervals"
        )

    return {
        "forecast_df": (
            pd.concat(all_forecasts, ignore_index=True)
            if all_forecasts
            else pd.DataFrame()
        ),
        "performance_metrics": (
            pd.concat(all_metrics, ignore_index=True)
            if all_metrics
            else pd.DataFrame()
        ),
        "model_insights": pd.DataFrame(all_insights),
        "plots": plots_dict,
        "visualization": _create_visualization_dict(
            "fit_stats_forecast_model", figures
        ),
    }


def _plot_forecast(
    df_plot: pd.DataFrame,
    metrics_df: pd.DataFrame,
    anomaly_dates: pd.Index,
    series_id: str,
    variable: str,
    h: int,
) -> Any:
    """
    Creates an interactive Plotly figure for the forecast results, including anomalies.
    """
    try:
        import plotly.graph_objects as go
    except ImportError:
        print("Plotly is not installed. Please run 'pip install plotly'.")
        return None

    # Identify the best model based on Mean Absolute Error (MAE)
    best_model_name = metrics_df.loc[series_id]["mae"].idxmin()

    fig = go.Figure()

    # Add historical data
    fig.add_trace(
        go.Scatter(
            x=df_plot["ds"],
            y=df_plot["y"],
            mode="lines",
            name="Observed",
            line=dict(color="black"),
        )
    )

    # Add forecast from the best model
    fig.add_trace(
        go.Scatter(
            x=df_plot["ds"].iloc[-h:],
            y=df_plot[best_model_name].iloc[-h:],
            mode="lines",
            name=f"Forecast ({best_model_name})",
            line=dict(color="blue", dash="dash"),
        )
    )

    # Add confidence intervals for the best model
    lo_col = next(
        (
            col
            for col in df_plot.columns
            if col.startswith(f"{best_model_name}-lo-")
        ),
        None,
    )
    hi_col = next(
        (
            col
            for col in df_plot.columns
            if col.startswith(f"{best_model_name}-hi-")
        ),
        None,
    )
    if lo_col and hi_col:
        fig.add_trace(
            go.Scatter(
                x=df_plot["ds"].iloc[-h:],
                y=df_plot[hi_col].iloc[-h:],
                mode="lines",
                line=dict(width=0),
                showlegend=False,
            )
        )
        fig.add_trace(
            go.Scatter(
                x=df_plot["ds"].iloc[-h:],
                y=df_plot[lo_col].iloc[-h:],
                mode="lines",
                line=dict(width=0),
                fill="tonexty",
                fillcolor="rgba(0, 0, 255, 0.1)",
                name="Confidence Interval",
            )
        )

    # Highlight anomalies detected from the best model's residuals
    if not anomaly_dates.empty:
        historical_y = df_plot.set_index("ds")["y"].dropna()
        # Ensure we only plot anomalies that exist in the historical data
        valid_anomalies = anomaly_dates[anomaly_dates.isin(historical_y.index)]
        fig.add_trace(
            go.Scatter(
                x=valid_anomalies,
                y=historical_y.loc[valid_anomalies],
                mode="markers",
                name="Anomalies",
                marker=dict(color="red", symbol="x", size=8),
            )
        )

    fig.update_layout(
        title=f"<b>Forecast for {series_id} - {variable} (Best Model: {best_model_name})</b>",
        xaxis_title="Date",
        yaxis_title=variable,
        legend=dict(x=0.01, y=0.99),
    )
    return fig


@register_tool
@tool_card(keywords=["ts", "time-series", "stationary", "test"])
def run_stationarity_test(
    df: Annotated[
        pd.DataFrame, "The input time-series data in a pandas DataFrame."
    ],
    value_cols: Annotated[
        List[str], "The column name(s) of the target variable(s) to be tested."
    ],
    ts_col: Annotated[
        str,
        "The column name of the timestamps. This column should be of a datetime type.",
    ],
    id_col: Annotated[
        Optional[str],
        "The column name of the identifier variable, indicating which series each row belongs to. If None, the function assumes a single time-series.",
    ] = None,
) -> pd.DataFrame:
    """
    Performs stationarity tests on time-series data and provides interpretable insights.

    This function conducts two common stationarity tests—the Augmented Dickey-Fuller (ADF)
    test and the Kwiatkowski-Phillips-Schmidt-Shin (KPSS) test—on one or more time series.
    It returns a pandas DataFrame summarizing the test results, including statistics,
    p-values, and a clear, easy-to-understand conclusion about the stationarity of each series.

    The ADF test checks for a unit root, where its null hypothesis is that the time series
    is **non-stationary**. The KPSS test, conversely, has a null hypothesis that the time series
    is **stationary**. Using both tests provides a more robust understanding of the data's
    stationarity characteristics.

    Parameters
    ----------
    df : pd.DataFrame
        The input DataFrame containing the time-series data.
    value_cols : List[str]
        A list of column names for the target variables that you want to test for stationarity.
    ts_col : str
        The name of the column that contains the timestamps. The data in this column should be
        of a datetime type.
    id_col : Optional[str], default=None
        The column name that identifies individual time series. If your DataFrame contains
        data for multiple series (e.g., sales data for different stores), this column
        distinguishes them. If not provided, the function treats the data as a single
        time series.

    Returns
    -------
    pd.DataFrame
        A DataFrame that presents the results of the stationarity tests. Each row
        corresponds to a time series (or a variable if no `id_col` is given) and includes:
        - The ADF test statistic and p-value.
        - The KPSS test statistic and p-value.
        - A final conclusion on whether the series is stationary, derived from the
          outcomes of both tests, with guidance on next steps.

    Examples
    --------
    >>> import pandas as pd
    >>> import numpy as np
    >>> # Create a sample DataFrame with a non-stationary series
    >>> data = {
    ...     'date': pd.to_datetime(pd.date_range(start='2023-01-01', periods=100, freq='D')),
    ...     'value': np.random.randn(100).cumsum() + 50
    ... }
    >>> time_series_df = pd.DataFrame(data)
    >>> # Run the stationarity test on the single series
    >>> stationarity_results = run_stationarity_test(
    ...     df=time_series_df,
    ...     value_cols=['value'],
    ...     ts_col='date'
    ... )
    >>> print(stationarity_results)
      series_id variable  adf_statistic  adf_p_value  kpss_statistic  kpss_p_value                 Conclusion
    0   Overall    value      -0.583262     0.874698        0.865391          0.01  The series is not stationary.
    """
    results = []
    df[ts_col] = pd.to_datetime(df[ts_col])

    if id_col:
        for series_id, group in df.groupby(id_col):
            for col in value_cols:
                # Ensure data is sorted by time for accurate testing
                series = (
                    group.sort_values(by=ts_col).set_index(ts_col)[col].dropna()
                )
                if not series.empty:
                    result = _perform_stationary_tests(series)
                    result["series_id"] = series_id
                    result["variable"] = col
                    results.append(result)
    else:
        for col in value_cols:
            series = df.sort_values(by=ts_col).set_index(ts_col)[col].dropna()
            if not series.empty:
                result = _perform_stationary_tests(series)
                result["series_id"] = "Overall"
                result["variable"] = col
                results.append(result)

    if not results:
        return pd.DataFrame(
            columns=[
                "series_id",
                "variable",
                "adf_statistic",
                "adf_p_value",
                "kpss_statistic",
                "kpss_p_value",
                "Conclusion",
            ]
        )

    results_df = pd.DataFrame(results)
    # Reorder columns for better readability
    cols_order = [
        "series_id",
        "variable",
        "adf_statistic",
        "adf_p_value",
        "kpss_statistic",
        "kpss_p_value",
        "Conclusion",
    ]
    results_df = results_df[cols_order]

    # Create visualizations
    figures = {}

    # 1. P-values comparison plot
    if not results_df.empty:
        fig1 = px.scatter(
            results_df,
            x="adf_p_value",
            y="kpss_p_value",
            color="Conclusion",
            hover_data=["series_id", "variable"],
            title="Stationarity Test Results: ADF vs KPSS P-Values",
            labels={
                "adf_p_value": "ADF P-Value",
                "kpss_p_value": "KPSS P-Value",
            },
        )
        # Add significance threshold lines
        fig1.add_hline(
            y=0.05,
            line_dash="dash",
            line_color="red",
            annotation_text="KPSS α = 0.05",
        )
        fig1.add_vline(
            x=0.05,
            line_dash="dash",
            line_color="red",
            annotation_text="ADF α = 0.05",
        )
        fig1_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"stationarity_pvalues_comparison_{uuid.uuid4().hex[:8]}.html",
        )
        fig1.write_html(fig1_path)
        figures[fig1_path] = (
            "Scatter plot comparing ADF and KPSS p-values with stationarity conclusions"
        )

        # 2. Test statistics bar chart
        melted_stats = pd.melt(
            results_df,
            id_vars=["series_id", "variable"],
            value_vars=["adf_statistic", "kpss_statistic"],
            var_name="Test",
            value_name="Statistic",
        )
        melted_stats["Series_Variable"] = (
            melted_stats["series_id"] + "_" + melted_stats["variable"]
        )

        fig2 = px.bar(
            melted_stats,
            x="Series_Variable",
            y="Statistic",
            color="Test",
            title="Stationarity Test Statistics by Series",
            labels={
                "Statistic": "Test Statistic Value",
                "Series_Variable": "Series_Variable",
            },
        )
        fig2.update_xaxes(tickangle=45)
        fig2_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"stationarity_test_statistics_{uuid.uuid4().hex[:8]}.html",
        )
        fig2.write_html(fig2_path)
        figures[fig2_path] = (
            "Bar chart showing ADF and KPSS test statistics for each time series"
        )

        # 3. Stationarity conclusions summary
        conclusion_counts = results_df["Conclusion"].value_counts()
        fig3 = px.pie(
            values=conclusion_counts.values,
            names=conclusion_counts.index,
            title="Distribution of Stationarity Conclusions",
        )
        fig3_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"stationarity_conclusions_summary_{uuid.uuid4().hex[:8]}.html",
        )
        fig3.write_html(fig3_path)
        figures[fig3_path] = (
            "Pie chart summarizing the distribution of stationarity test conclusions"
        )

    return {
        "results": results_df,
        "visualization": _create_visualization_dict(
            "run_stationarity_test", figures
        ),
    }


def _perform_stationary_tests(
    series: pd.Series,
) -> Dict[str, Union[str, float]]:
    """Helper function to perform ADF and KPSS tests on a single series."""
    # Augmented Dickey-Fuller (ADF) Test
    # Null Hypothesis (H0): The series has a unit root (is non-stationary).
    # p-value <= 0.05: Reject H0, series is stationary.
    adf_result = adfuller(series, autolag="AIC")
    adf_p_value = adf_result[1]

    # Kwiatkowski-Phillips-Schmidt-Shin (KPSS) Test
    # Null Hypothesis (H0): The series is stationary around a constant (level stationary).
    # p-value < 0.05: Reject H0, series is non-stationary.
    # Note: We use 'c' for level stationarity. 'ct' would be for trend stationarity.
    kpss_result = kpss(series, regression="c", nlags="auto")
    kpss_p_value = kpss_result[1]

    # Combine results for a more robust conclusion
    if adf_p_value > 0.05 and kpss_p_value < 0.05:
        conclusion = "The series is not stationary."
    elif adf_p_value <= 0.05 and kpss_p_value > 0.05:
        conclusion = "The series is stationary."
    elif adf_p_value > 0.05 and kpss_p_value > 0.05:
        conclusion = (
            "The series is trend stationary. Consider removing the trend."
        )
    else:  # adf_p_value <= 0.05 and kpss_p_value < 0.05
        conclusion = "The series is difference stationary. Consider differencing the data."

    return {
        "adf_statistic": adf_result[0],
        "adf_p_value": adf_p_value,
        "kpss_statistic": kpss_result[0],
        "kpss_p_value": kpss_p_value,
        "Conclusion": conclusion,
    }


@register_tool
@tool_card(
    keywords=["ts", "time-series", "decomposition", "trend", "seasonality"]
)
def run_time_series_decomposition(
    df: Annotated[
        pd.DataFrame, "The input time-series data in a pandas DataFrame."
    ],
    value_cols: Annotated[
        List[str],
        "The column name(s) of the target variable(s) to be decomposed.",
    ],
    ts_col: Annotated[
        str,
        "The column name of the timestamps. This column should be of a datetime type.",
    ],
    id_col: Annotated[
        Optional[str],
        "The column name of the identifier variable, indicating which series each row belongs to. If None, the function assumes a single time-series.",
    ] = None,
) -> Dict[str, Any]:
    """
    Performs time-series decomposition, models the trend, and provides visualizations.

    This function automatically decomposes time series into trend, seasonal, and
    residual components. It intelligently determines the best seasonal period and
    model (additive/multiplicative).

    It enhances the analysis by:
    1.  **Fitting a polynomial model** (linear or quadratic) to the trend component
        to provide a simple mathematical description of the trend's behavior.
    2.  **Generating interactive plots** using Plotly for each decomposition,
        allowing for easy visual inspection of the components.

    Parameters
    ----------
    df : pd.DataFrame
        The input DataFrame containing the time-series data.
    value_cols : List[str]
        A list of column names for the target variables to be decomposed.
    ts_col : str
        The name of the column that contains the timestamps.
    id_col : Optional[str], default=None
        The column name that identifies individual time series.

    Returns
    -------
    Dict[str, Any]
        A dictionary containing two key-value pairs:
        - **'insights'**: A pandas DataFrame with quantitative results, including the
          best model, seasonal period, trend/seasonality strengths, and the fitted
          trend equation.
        - **'plots'**: A dictionary where keys are tuples of (series_id, variable)
          and values are the corresponding interactive Plotly figure objects.

    Examples
    --------
    >>> import pandas as pd
    >>> import numpy as np
    >>> # Create sample data
    >>> dates = pd.to_datetime(pd.date_range(start='2023-01-01', periods=180, freq='D'))
    >>> seasonal_pattern = np.sin(np.linspace(0, (180/7) * 2 * np.pi, 180)) * 10
    >>> trend = np.linspace(0, 20, 180)
    >>> sales_data = 50 + trend + seasonal_pattern + np.random.normal(0, 1, 180)
    >>> time_series_df = pd.DataFrame({'date': dates, 'sales': sales_data})
    >>> # Run the decomposition
    >>> results = run_time_series_decomposition(
    ...     df=time_series_df,
    ...     value_cols=['sales'],
    ...     ts_col='date'
    ... )
    >>> # Print the insights DataFrame
    >>> print(results['insights'])
      series_id variable best_model  seasonal_period  ... seasonality_strength                                 trend_equation
    0   Overall    sales   additive                7  ...               0.975497  Trend = 0.1117 * t + 49.4444
    <BLANKLINE>
    >>> # To show a plot (in a compatible environment like Jupyter):
    >>> # sales_plot = results['plots'][('Overall', 'sales')]
    >>> # sales_plot.show()
    """
    insights_list = []
    plots_dict = {}
    df[ts_col] = pd.to_datetime(df[ts_col])

    id_col_internal = id_col if id_col else "__dummy_id__"
    if not id_col:
        df[id_col_internal] = "Overall"

    for series_id, group in df.groupby(id_col_internal):
        for col in value_cols:
            series = (
                group.sort_values(by=ts_col).set_index(ts_col)[col].dropna()
            )
            if len(series) < 4:
                continue

            best_decomp_params = _get_best_decomposition(series)
            if not best_decomp_params:
                insights_list.append(
                    {
                        "series_id": series_id,
                        "variable": col,
                        "best_model": "N/A",
                        "seasonal_period": 0,
                        "trend_strength": 0,
                        "seasonality_strength": 0,
                        "trend_equation": "N/A",
                        "trend_coeffs": [],
                    }
                )
                continue

            decomp = best_decomp_params["decomposition"]
            trend_info = _fit_trend(decomp.trend)
            plots_dict[(series_id, col)] = _plot_decomposition(
                decomp, series_id, col
            )

            var_resid = np.var(decomp.resid.dropna())
            if best_decomp_params["model"] == "additive":
                var_trend_plus_resid = np.var(
                    (decomp.trend + decomp.resid).dropna()
                )
                var_seasonal_plus_resid = np.var(
                    (decomp.seasonal + decomp.resid).dropna()
                )
            else:
                var_trend_plus_resid = np.var(
                    (decomp.trend * decomp.resid).dropna()
                )
                var_seasonal_plus_resid = np.var(
                    (decomp.seasonal * decomp.resid).dropna()
                )

            insights_list.append(
                {
                    "series_id": series_id,
                    "variable": col,
                    "best_model": best_decomp_params["model"],
                    "seasonal_period": best_decomp_params["period"],
                    "trend_strength": (
                        max(0, 1 - var_resid / var_trend_plus_resid)
                        if var_trend_plus_resid > 0
                        else 0
                    ),
                    "seasonality_strength": (
                        max(0, 1 - var_resid / var_seasonal_plus_resid)
                        if var_seasonal_plus_resid > 0
                        else 0
                    ),
                    **trend_info,
                }
            )

    if "__dummy_id__" in df.columns:
        df.drop(columns=["__dummy_id__"], inplace=True)

    insights_df = pd.DataFrame(insights_list)
    if not insights_df.empty:
        cols_order = [
            "series_id",
            "variable",
            "best_model",
            "seasonal_period",
            "trend_strength",
            "seasonality_strength",
            "trend_equation",
            "trend_coeffs",
        ]
        insights_df = insights_df[cols_order]

    # Create additional visualizations
    figures = {}

    # 1. Trend and seasonality strength comparison
    if not insights_df.empty:
        fig1 = px.scatter(
            insights_df,
            x="trend_strength",
            y="seasonality_strength",
            color="best_model",
            size="seasonal_period",
            hover_data=["series_id", "variable"],
            title="Time Series Components: Trend vs Seasonality Strength",
            labels={
                "trend_strength": "Trend Strength",
                "seasonality_strength": "Seasonality Strength",
            },
        )
        fig1_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"decomposition_strength_comparison_{uuid.uuid4().hex[:8]}.html",
        )
        fig1.write_html(fig1_path)
        figures[fig1_path] = (
            "Scatter plot comparing trend and seasonality strength across time series"
        )

        # 2. Seasonal period distribution
        if "seasonal_period" in insights_df.columns:
            period_counts = insights_df["seasonal_period"].value_counts()
            fig2 = px.bar(
                x=period_counts.index,
                y=period_counts.values,
                title="Distribution of Seasonal Periods",
                labels={"x": "Seasonal Period", "y": "Count"},
            )
            fig2_path = os.path.join(
                CODE_OUTPUT_DIR,
                f"decomposition_seasonal_periods_{uuid.uuid4().hex[:8]}.html",
            )
            fig2.write_html(fig2_path)
            figures[fig2_path] = (
                "Bar chart showing the distribution of detected seasonal periods"
            )

        # 3. Decomposition model comparison
        model_counts = insights_df["best_model"].value_counts()
        fig3 = px.pie(
            values=model_counts.values,
            names=model_counts.index,
            title="Distribution of Best Decomposition Models",
        )
        fig3_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"decomposition_model_distribution_{uuid.uuid4().hex[:8]}.html",
        )
        fig3.write_html(fig3_path)
        figures[fig3_path] = (
            "Pie chart showing the distribution of optimal decomposition models (additive vs multiplicative)"
        )

    # 4. Save existing plots from plots_dict
    for (series_id, col), fig in plots_dict.items():
        fig_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"decomposition_timeseries_{series_id}_{col}_{uuid.uuid4().hex[:8]}.html",
        )
        fig.write_html(fig_path)
        figures[fig_path] = (
            f"Time series decomposition plot for {col} (series: {series_id}) showing original, trend, seasonal, and residual components"
        )

    return {
        "insights": insights_df,
        "plots": plots_dict,
        "visualization": _create_visualization_dict(
            "run_time_series_decomposition", figures
        ),
    }


def _fit_trend(trend_series: pd.Series) -> Dict[str, Any]:
    """
    Fits a linear or quadratic model to the trend component.

    It uses the Bayesian Information Criterion (BIC) to select the best model.
    """
    trend_data = trend_series.dropna()
    t = np.arange(len(trend_data))
    y = trend_data.values

    # Fit linear model (degree 1)
    coeffs_lin = np.polyfit(t, y, 1)
    pred_lin = np.polyval(coeffs_lin, t)
    rss_lin = np.sum((y - pred_lin) ** 2)
    bic_lin = len(t) * np.log(rss_lin / len(t)) + 2 * np.log(len(t))

    # Fit quadratic model (degree 2)
    coeffs_quad = np.polyfit(t, y, 2)
    pred_quad = np.polyval(coeffs_quad, t)
    rss_quad = np.sum((y - pred_quad) ** 2)
    bic_quad = len(t) * np.log(rss_quad / len(t)) + 3 * np.log(len(t))

    if bic_lin <= bic_quad:
        # Linear model is better or comparable
        equation = f"Trend = {coeffs_lin[0]:.4f} * t + {coeffs_lin[1]:.4f}"
        return {"trend_equation": equation, "trend_coeffs": coeffs_lin}
    else:
        # Quadratic model is better
        equation = f"Trend = {coeffs_quad[0]:.4f} * t^2 + {coeffs_quad[1]:.4f} * t + {coeffs_quad[2]:.4f}"
        return {"trend_equation": equation, "trend_coeffs": coeffs_quad}


def _plot_decomposition(
    decomp: Any, series_id: str, variable: str
) -> go.Figure:
    """Creates an interactive Plotly figure for the decomposition results."""
    fig = make_subplots(
        rows=4,
        cols=1,
        shared_xaxes=True,
        subplot_titles=("Observed", "Trend", "Seasonal", "Residual"),
        vertical_spacing=0.06,
    )

    fig.add_trace(
        go.Scatter(
            x=decomp.observed.index,
            y=decomp.observed,
            name="Observed",
            mode="lines",
            line=dict(color="black"),
        ),
        row=1,
        col=1,
    )
    fig.add_trace(
        go.Scatter(
            x=decomp.trend.index,
            y=decomp.trend,
            name="Trend",
            mode="lines",
            line=dict(color="blue"),
        ),
        row=2,
        col=1,
    )
    fig.add_trace(
        go.Scatter(
            x=decomp.seasonal.index,
            y=decomp.seasonal,
            name="Seasonal",
            mode="lines",
            line=dict(color="green"),
        ),
        row=3,
        col=1,
    )
    fig.add_trace(
        go.Scatter(
            x=decomp.resid.index,
            y=decomp.resid,
            name="Residual",
            mode="markers",
            marker=dict(color="red", size=4),
        ),
        row=4,
        col=1,
    )

    fig.update_layout(
        title_text=f"<b>Time-Series Decomposition: {series_id} - {variable}</b>",
        height=700,
        showlegend=False,
    )
    return fig


def _get_best_decomposition(
    series: pd.Series,
) -> Optional[Dict[str, Any]]:
    """
    Automatically finds the best decomposition model and seasonal period.
    (This helper function is unchanged from the previous version)
    """
    best_params = {
        "model": None,
        "period": None,
        "variance": np.inf,
        "decomposition": None,
    }
    try:
        max_lags = min(len(series) // 2, 260)
        if max_lags < 4:
            return None
        acf_vals = acf(series, nlags=max_lags, fft=True)
        peaks, _ = find_peaks(acf_vals, height=0.1, distance=2)
        potential_periods = set(peaks[peaks > 1]) | {7, 12, 52}
    except Exception:
        potential_periods = {7, 12, 52}

    for period in potential_periods:
        if period >= len(series) // 2:
            continue
        try:
            decomp_add = seasonal_decompose(
                series, model="additive", period=period
            )
            res_var_add = np.var(decomp_add.resid.dropna())
            if res_var_add < best_params["variance"]:
                best_params.update(
                    {
                        "model": "additive",
                        "period": period,
                        "variance": res_var_add,
                        "decomposition": decomp_add,
                    }
                )
        except Exception:
            pass
        if (series > 0).all():
            try:
                decomp_mult = seasonal_decompose(
                    series, model="multiplicative", period=period
                )
                res_var_mult = np.var(decomp_mult.resid.dropna())
                if res_var_mult < best_params["variance"]:
                    best_params.update(
                        {
                            "model": "multiplicative",
                            "period": period,
                            "variance": res_var_mult,
                            "decomposition": decomp_mult,
                        }
                    )
            except Exception:
                pass

    return best_params if best_params["decomposition"] else None


@register_tool
@tool_card(keywords=["unsupervised", "cluster", "k-means", "hierarchical"])
def run_clustering(
    data: Annotated[pd.DataFrame, "The input pandas DataFrame."],
    cols: Annotated[
        List[str], "The column names of the variables to be clustered"
    ],
    model_type: Annotated[
        str,
        "The type of clustering algorithm. Supports 'kmeans' and 'ap' (affinity propagation).",
    ],
    n_clusters: Annotated[
        Optional[int],
        "The number of clusters to form. Required if model_type is 'kmeans'.",
    ] = None,
    damping: Annotated[
        Optional[float],
        "Damping factor for Affinity Propagation. Must be between 0.5 and 1.",
    ] = 0.75,
) -> Dict[str, Any]:
    """
        Performs clustering on the given data to group similar data points together.

        This function helps identify patterns and relationships in the data by
        partitioning it into distinct subgroups or clusters.

        --- Algorithm Options ---

        'kmeans': K-Means Clustering
            - How it works: Partitions the data into a pre-determined number (`k`)
              of clusters. It iteratively assigns each data point to the nearest
              cluster center (mean) and then recalculates the center, until the
              assignments stabilize.
            - Pros: Simple to understand, fast, and scales well to large datasets. It's
              often a good starting point for clustering tasks.
            - Cons: You MUST specify the number of clusters (`k`) beforehand, which
              can be difficult if you don't know your data well. The result can depend
              on the initial random placement of centroids. It works best for spherical,
              evenly-sized clusters.

        'ap': Affinity Propagation
            - How it works: A more advanced algorithm that does not require you to
              specify the number of clusters. It works by treating each data point as a
              potential 'exemplar' (a representative for a cluster) and exchanging
              messages between data points until a consensus on the best set of
    d         exemplars and clusters emerges.
            - Pros: The main advantage is that it automatically determines the number
              of clusters from the data itself.
            - Cons: It is much slower and more memory-intensive than K-Means, making
              it less suitable for very large datasets. Its performance can be sensitive
              to the 'damping' parameter.

        Args:
            data: The input pandas DataFrame.
            cols: A list of column names for the variables to be used in clustering.
            model_type: The type of clustering algorithm to use.
            n_clusters: The number of clusters. Required for 'kmeans'.
            damping: The damping factor for 'ap' (Affinity Propagation).

        Returns:
            A dictionary containing the clustering results:
            - 'model': The fitted clustering model object.
            - 'labels': An array with the cluster label for each data point.
            - 'cluster_centers': A DataFrame with the coordinates of each cluster center.
            - 'data_with_labels': The original data with a new 'cluster_label' column.
    """
    # --- 1. Input Validation ---
    if not isinstance(data, pd.DataFrame):
        raise TypeError("The 'data' argument must be a pandas DataFrame.")
    for col in cols:
        if col not in data.columns:
            raise ValueError(f"Column '{col}' not found in the DataFrame.")
        if not pd.api.types.is_numeric_dtype(data[col]):
            raise ValueError(f"Column '{col}' must be numeric for clustering.")

    if model_type == "kmeans" and (n_clusters is None or n_clusters < 1):
        raise ValueError(
            "For 'kmeans', you must provide a positive 'n_clusters'."
        )

    # --- 2. Data Preparation ---
    print(f"Preparing data from columns: {cols}")
    X = data[cols]

    # Scaling is crucial for distance-based algorithms like K-Means and AP
    # to ensure all features contribute equally.
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    fitted_model = None
    labels = None
    cluster_centers = None

    # --- 3. Model Selection and Fitting ---
    if model_type == "kmeans":
        print(f"Performing K-Means clustering with k={n_clusters}...")
        model = KMeans(n_clusters=n_clusters, random_state=42, n_init="auto")
        fitted_model = model.fit(X_scaled)
        labels = fitted_model.labels_
        # Inverse transform the centers to get them in the original data scale
        cluster_centers = scaler.inverse_transform(
            fitted_model.cluster_centers_
        )

    elif model_type == "ap":
        print("Performing Affinity Propagation clustering...")
        model = AffinityPropagation(damping=damping, random_state=42)
        fitted_model = model.fit(X_scaled)
        labels = fitted_model.labels_
        cluster_centers = scaler.inverse_transform(
            fitted_model.cluster_centers_
        )

    else:
        raise ValueError(
            f"Unsupported model_type: '{model_type}'. Supported types are 'kmeans' and 'ap'."
        )

    # --- 4. Format and Return Results ---
    data_with_labels = data.copy()
    data_with_labels["cluster_label"] = labels

    cluster_centers_df = pd.DataFrame(
        cluster_centers,
        columns=cols,
        index=[f"cluster_{i}" for i in range(len(cluster_centers))],
    )

    num_found_clusters = len(cluster_centers)
    print(f"Clustering complete. Found {num_found_clusters} clusters.")

    # Create visualizations
    figures = {}

    # 1. Cluster scatter plot (2D projection if more than 2 features)
    if len(cols) >= 2:
        if len(cols) == 2:
            # Direct 2D plot
            fig1 = px.scatter(
                data_with_labels,
                x=cols[0],
                y=cols[1],
                color="cluster_label",
                title=f"{model_type.title()} Clustering Results",
                labels={"cluster_label": "Cluster"},
            )
            # Add cluster centers
            fig1.add_trace(
                go.Scatter(
                    x=cluster_centers_df[cols[0]],
                    y=cluster_centers_df[cols[1]],
                    mode="markers",
                    marker=dict(symbol="x", size=15, color="black"),
                    name="Cluster Centers",
                )
            )
        else:
            # Use first two features for visualization
            fig1 = px.scatter(
                data_with_labels,
                x=cols[0],
                y=cols[1],
                color="cluster_label",
                title=f"{model_type.title()} Clustering Results (2D Projection)",
                labels={"cluster_label": "Cluster"},
            )
            # Add cluster centers
            fig1.add_trace(
                go.Scatter(
                    x=cluster_centers_df[cols[0]],
                    y=cluster_centers_df[cols[1]],
                    mode="markers",
                    marker=dict(symbol="x", size=15, color="black"),
                    name="Cluster Centers",
                )
            )

        fig1_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"clustering_scatter_plot_{uuid.uuid4().hex[:8]}.html",
        )
        fig1.write_html(fig1_path)
        figures[fig1_path] = (
            f"Scatter plot showing {model_type} clustering results with {num_found_clusters} clusters"
        )

    # 2. Cluster size distribution
    cluster_counts = pd.Series(labels).value_counts().sort_index()
    fig2 = px.bar(
        x=[f"Cluster {i}" for i in cluster_counts.index],
        y=cluster_counts.values,
        title="Cluster Size Distribution",
        labels={"x": "Cluster", "y": "Number of Points"},
    )
    fig2_path = os.path.join(
        CODE_OUTPUT_DIR,
        f"clustering_size_distribution_{uuid.uuid4().hex[:8]}.html",
    )
    fig2.write_html(fig2_path)
    figures[fig2_path] = (
        f"Bar chart showing the distribution of data points across {num_found_clusters} clusters"
    )

    # 3. Feature comparison across clusters (heatmap)
    cluster_means = data_with_labels.groupby("cluster_label")[cols].mean()
    fig3 = px.imshow(
        cluster_means.T,
        labels=dict(x="Cluster", y="Feature", color="Mean Value"),
        title="Cluster Characteristics Heatmap",
        aspect="auto",
    )
    fig3_path = os.path.join(
        CODE_OUTPUT_DIR,
        f"clustering_characteristics_heatmap_{uuid.uuid4().hex[:8]}.html",
    )
    fig3.write_html(fig3_path)
    figures[fig3_path] = (
        "Heatmap showing the mean values of each feature across different clusters"
    )

    # 4. Parallel coordinates plot for multi-dimensional visualization
    if len(cols) > 2:
        # Normalize data for better visualization
        normalized_data = data_with_labels.copy()
        for col in cols:
            normalized_data[col] = (
                normalized_data[col] - normalized_data[col].min()
            ) / (normalized_data[col].max() - normalized_data[col].min())

        fig4 = px.parallel_coordinates(
            normalized_data,
            color="cluster_label",
            dimensions=cols,
            title="Parallel Coordinates Plot of Clusters",
            labels={"cluster_label": "Cluster"},
        )
        fig4_path = os.path.join(
            CODE_OUTPUT_DIR,
            f"clustering_parallel_coordinates_{uuid.uuid4().hex[:8]}.html",
        )
        fig4.write_html(fig4_path)
        figures[fig4_path] = (
            f"Parallel coordinates plot showing cluster patterns across all {len(cols)} features"
        )

    return {
        "model": fitted_model,
        "labels": labels,
        "cluster_centers": cluster_centers_df,
        "data_with_labels": data_with_labels,
        "visualization": _create_visualization_dict("run_clustering", figures),
    }
