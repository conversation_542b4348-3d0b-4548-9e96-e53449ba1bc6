"""
This submodule contains the expert tool functions for advanced data science tasks,
such as machine learning, forecasting, and optimization.
"""

# from .data_preprocessing import (
#     preprocess_features_for_modeling,
#     split_data_for_modeling,
# )
# from .data_visualization import (
#     create_bar_plot,
#     create_box_plot,
#     create_correlation_heatmap,
#     create_histogram,
#     create_line_plot,
#     create_scatter_plot,
# )
# from .model_evaluation import (
#     evaluate_model_performance,
#     format_prediction_report,
#     interpret_trained_model,
#     run_prediction,
# )
# from .model_training import train_classification_model, train_regression_model
# from .unsupervised_learning import (
#     evaluate_clustering_performance,
#     preprocess_for_unsupervised,
#     run_anomaly_detection,
#     run_clustering,
#     run_dimensionality_reduction,
# )

# __all__ = [
#     # Data Preprocessing
#     "split_data_for_modeling",
#     "preprocess_features_for_modeling",
#     # Model Training
#     "train_regression_model",
#     "train_classification_model",
#     # Model Evaluation & Reporting
#     "run_prediction",
#     "evaluate_model_performance",
#     "interpret_trained_model",
#     "format_prediction_report",
#     # Data Cleaning
#     "detect_missing_values",
#     "handle_missing_values",
#     "detect_outliers",
#     "handle_outliers",
#     "normalize_data",
#     # Data Visualization
#     "create_histogram",
#     "create_scatter_plot",
#     "create_bar_plot",
#     "create_line_plot",
#     "create_box_plot",
#     "create_correlation_heatmap",
#     # Unsupervised Learning
#     "preprocess_for_unsupervised",
#     "run_clustering",
#     "evaluate_clustering_performance",
#     "run_dimensionality_reduction",
#     "run_anomaly_detection",
# ]

from .pseudo_tool_list import (
    fit_advanced_regression_model,
    fit_classification_model,
    fit_linear_regression_model,
    fit_stats_forecast_model,
    run_clustering,
    run_stationarity_test,
    run_statistical_test,
    run_time_series_decomposition,
)

__all__ = [
    # Pseudo Tools
    "fit_advanced_regression_model",
    "fit_classification_model",
    "fit_linear_regression_model",
    "fit_stats_forecast_model",
    "run_clustering",
    "run_stationarity_test",
    "run_statistical_test",
    "run_time_series_decomposition",
]
