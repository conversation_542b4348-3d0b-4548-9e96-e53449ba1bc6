import hashlib
import json
import operator
import os
import sqlite3
from enum import Enum
from pathlib import Path
from pprint import pprint
from typing import Annotated, Any, Dict, List, TypedDict
from uuid import uuid4

import pandas as pd
import polars as pl
from langchain.chat_models import init_chat_model
from langchain.output_parsers import RetryOutputParser
from langchain_core.messages import (
    AIMessage,
    AnyMessage,
    BaseMessage,
    HumanMessage,
    SystemMessage,
)
from langchain_core.output_parsers import JsonOutputParser, StrOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import <PERSON>nableLambda
from langgraph.checkpoint.sqlite import SqliteSaver
from langgraph.graph import END, START, MessagesState, StateGraph, add_messages

# --- Assume these imports match your project structure ---
from langgraph_da_backend.basic_tools.data_cleaning import (
    handle_duplicates,
    handle_missing_values,
    handle_outliers,
    perform_normalization,
    perform_sorting,
)
from langgraph_da_backend.basic_tools.data_describe import describe
from langgraph_da_backend.exec_engine.utils import (
    extract_python_code,
    run_sandboxed_code,
)
from langgraph_da_backend.expert_tools.pseudo_tool_list import *
from langgraph_da_backend.schemas.agent import DataSource
from langgraph_da_backend.utils.loader.csv import load as load_csv
from langgraph_da_backend.utils.static.prompt_schema import (
    AbstractPlanSchema,
    DataProcessingPipelineSchema,
    DescriptiveSufficiencyCheckSchema,
    FinalAnswerSchema,
    FunctionExecutionSchema,
    RequestSanityCheckSchema,
    RewriteSchema,
)
from langgraph_da_backend.utils.static.prompt_template import (
    ABSTRACT_PLAN_PARSE_TEMPLATE,
    ABSTRACT_PLAN_TEMPLATE,
    DA_CODE_GENERATION_TEMPLATE,
    DA_DESCRIPTIVE_SUFFICIENCY_TEMPLATE,
    DA_FINAL_ANSWER_TEMPLATE,
    DA_FUNCTION_EXECUTION_TEMPLATE,
    DA_REWRITE_TEMPLATE,
    DA_SANITY_CHECK_TEMPLATE,
    DE_CODE_CURATION_TEMPLATE,
    DE_PIPELINE_TEMPLATE,
)
from langgraph_da_backend.utils.tool.tool_card import REGISTERED_TOOLS
from langgraph_da_backend.vector_search.dbmanager import DatabaseManager
from langgraph_da_backend.vector_search.embed import CustomEmbeddingFunction
from loguru import logger
from pydantic import BaseModel, Field

# --- Data Utilities for LangGraph State Management ---


def serialize_dataframe(df) -> Dict[str, Any]:
    """Serialize a polars LazyFrame or pandas DataFrame to a dictionary for state storage."""
    if df is None:
        return None

    # Handle polars LazyFrame
    if hasattr(df, "collect"):  # polars LazyFrame
        collected_df = df.collect()
        return {
            "data": collected_df.to_dicts(),
            "schema": {
                name: str(dtype) for name, dtype in collected_df.schema.items()
            },
            "type": "polars",
        }
    # Handle pandas DataFrame
    elif hasattr(df, "to_dict"):  # pandas DataFrame
        return {
            "data": df.to_dict(orient="records"),
            "columns": df.columns.tolist(),
            "index": df.index.tolist(),
            "dtypes": df.dtypes.astype(str).to_dict(),
            "type": "pandas",
        }
    else:
        raise ValueError(f"Unsupported data type for serialization: {type(df)}")


def deserialize_dataframe(data_dict: Dict[str, Any]):
    """Deserialize a dictionary back to a polars LazyFrame or pandas DataFrame."""
    if data_dict is None:
        return None

    data_type = data_dict.get(
        "type", "pandas"
    )  # Default to pandas for backward compatibility

    if data_type == "polars":
        # Recreate polars DataFrame and return as LazyFrame
        df = pl.DataFrame(data_dict["data"])
        return df.lazy()
    else:  # pandas
        df = pd.DataFrame(data_dict["data"])
        # Restore column order and dtypes
        if "columns" in data_dict:
            df = df[data_dict["columns"]]
        if "dtypes" in data_dict:
            for col, dtype in data_dict["dtypes"].items():
                try:
                    if dtype.startswith("int"):
                        df[col] = pd.to_numeric(
                            df[col], errors="coerce"
                        ).astype("Int64")
                    elif dtype.startswith("float"):
                        df[col] = pd.to_numeric(df[col], errors="coerce")
                    elif dtype == "bool":
                        df[col] = df[col].astype(bool)
                    # Keep object/string columns as-is
                except Exception:
                    pass  # Keep original if conversion fails
        return df


def _prepare_dataframe_for_tool(df: Any) -> str:
    """
    Converts a dataframe to JSON string format expected by tool functions.

    Args:
        df: DataFrame object (pandas, polars DataFrame or LazyFrame)

    Returns:
        str: JSON string representation of the dataframe
    """
    try:
        # Handle different dataframe types
        if hasattr(df, "to_pandas"):
            # Polars DataFrame/LazyFrame
            if hasattr(df, "collect"):
                # LazyFrame - collect first
                pandas_df = df.collect().to_pandas()
            else:
                # DataFrame
                pandas_df = df.to_pandas()
        elif hasattr(df, "to_dict"):
            # Pandas DataFrame
            pandas_df = df
        else:
            raise ValueError(f"Unsupported dataframe type: {type(df)}")

        # Convert to JSON string
        # Use orient='records' to get list of dictionaries format
        return pandas_df.to_json(orient="records")

    except Exception as e:
        logger.error(f"Error converting dataframe to JSON: {str(e)}")
        raise ValueError(
            f"Failed to convert dataframe to tool format: {str(e)}"
        )


# --- Modified AgentState ---
class AgentState(TypedDict):
    messages: Annotated[List[AnyMessage], add_messages]
    current_user_prompt: str
    terminate_signal: bool
    target_data_sources: List[str]
    # Store actual data objects instead of file paths for LangGraph persistence
    loaded_data_sources: List[
        Dict[str, Any]
    ]  # List of {uri, protocol, data_dict}
    refined_data: Dict[str, Any] | None  # Serialized DataFrame data
    renewed_data: Dict[str, Any] | None  # Serialized DataFrame data
    abstract_plan_desc: str
    abstract_plan_feedback: str
    reflection_hint: str
    tool_exec_results: List[Any]
    execution_reflection_feedback: str
    summary_report: Any


# --- Updated Graph Nodes with Original Logic Restored ---


def standby(state: AgentState) -> AgentState:
    """
    Standby node: Load data sources using LangGraph state persistence.
    """
    try:
        print("🔄 STANDBY: Starting data loading and initialization...")

        # Check if data is already loaded (for resumption)
        if (
            state.get("loaded_data_sources")
            and len(state["loaded_data_sources"]) > 0
        ):
            print(
                f"✅ Found {len(state['loaded_data_sources'])} previously loaded data sources"
            )
            return state

        if state.get("target_data_sources"):
            loaded_sources = []

            for data_source_uri in state["target_data_sources"]:
                try:
                    print(f"📁 Loading data from: {data_source_uri}")
                    uri = Path(data_source_uri).resolve().as_posix()
                    d = load_csv(uri)  # This returns a polars LazyFrame
                    protocol = "csv"

                    # Serialize the polars LazyFrame for state storage
                    serialized_data = serialize_dataframe(d)

                    # Store in the new format
                    data_source_obj = {
                        "uri": uri,
                        "protocol": protocol,
                        "data": serialized_data,
                    }
                    loaded_sources.append(data_source_obj)
                    print(f"✅ Successfully loaded data from: {uri}")

                except FileNotFoundError as e:
                    error_msg = f"File not found: {data_source_uri}. Please check the file path."
                    logger.error(error_msg)
                    print(f"❌ FILE ERROR: {error_msg}")
                    state["terminate_signal"] = True
                    if not state.get("messages"):
                        state["messages"] = []
                    state["messages"].append(
                        AIMessage(content=f"❌ Error: {error_msg}")
                    )
                    return state
                except Exception as e:
                    error_msg = (
                        f"Failed to load data from {data_source_uri}: {str(e)}"
                    )
                    logger.error(error_msg)
                    print(f"❌ DATA LOADING ERROR: {error_msg}")
                    state["terminate_signal"] = True
                    if not state.get("messages"):
                        state["messages"] = []
                    state["messages"].append(
                        AIMessage(content=f"❌ Error: {error_msg}")
                    )
                    return state

            # Store loaded data sources in state
            state["loaded_data_sources"] = loaded_sources
            print(
                f"✅ STANDBY: Loaded {len(loaded_sources)} data sources successfully"
            )

        if state.get("current_user_prompt"):
            if not state.get("messages"):
                state["messages"] = []
            state["messages"].append(
                HumanMessage(content=state["current_user_prompt"])
            )

        state["target_data_sources"] = []
        print("✅ STANDBY: Completed successfully")
        return state

    except Exception as e:
        error_msg = f"Unexpected error in standby node: {str(e)}"
        logger.error(error_msg)
        print(f"❌ STANDBY SYSTEM ERROR: {error_msg}")

        # Print full traceback for debugging
        import traceback

        print(f"📋 Full traceback:")
        traceback.print_exc()

        # Ensure we have a messages list
        if not state.get("messages"):
            state["messages"] = []

        # Add error message and set termination signal
        state["messages"].append(
            AIMessage(content=f"❌ System Error in data loading: {error_msg}")
        )
        state["terminate_signal"] = True
        return state


def standby_routing(state: AgentState) -> str:
    if state.get("terminate_signal"):
        return "END"
    elif state.get("loaded_data_sources") and state.get("current_user_prompt"):
        return "abstract_plan"
    else:
        return "standby"


def abstract_plan(state: AgentState) -> AgentState:
    """
    Abstract plan node with comprehensive error handling.
    """
    try:
        print("🔄 ABSTRACT_PLAN: Starting analysis planning...")

        # Validate required environment variables
        if not os.getenv("OPENAI_API_KEY"):
            error_msg = "OPENAI_API_KEY environment variable is not set. Please configure your API key."
            logger.error(error_msg)
            print(f"❌ CONFIG ERROR: {error_msg}")
            state["terminate_signal"] = True
            if not state.get("messages"):
                state["messages"] = []
            state["messages"].append(
                AIMessage(content=f"❌ Configuration Error: {error_msg}")
            )
            return state

        # Load data from state for planning
        if (
            not state.get("loaded_data_sources")
            or not state["loaded_data_sources"]
        ):
            error_msg = "No data loaded. Cannot proceed with planning."
            logger.error(error_msg)
            print(f"❌ DATA ERROR: {error_msg}")
            state["terminate_signal"] = True
            if not state.get("messages"):
                state["messages"] = []
            state["messages"].append(
                AIMessage(content=f"❌ Error: {error_msg}")
            )
            return state

        try:
            # Deserialize the first data source for planning (already returns polars LazyFrame)
            first_data_source = state["loaded_data_sources"][0]
            data_for_planning = deserialize_dataframe(first_data_source["data"])
            # Get shape info for logging
            collected_df = data_for_planning.collect()
            print(f"📊 Loaded data for planning: {collected_df.shape}")
        except Exception as e:
            error_msg = f"Failed to deserialize data from state: {str(e)}"
            logger.error(error_msg)
            print(f"❌ DATA DESERIALIZATION ERROR: {error_msg}")
            state["terminate_signal"] = True
            if not state.get("messages"):
                state["messages"] = []
            state["messages"].append(
                AIMessage(content=f"❌ Error: {error_msg}")
            )
            return state

        # Initialize LLM with error handling
        try:
            print("🤖 Initializing LLM...")
            llm = init_chat_model(
                "gpt-4.1",
                model_provider="openai",
                base_url="https://api.apiyi.com/v1",
                streaming=False,
                temperature=0,
                use_responses_api=False,
            )
        except Exception as e:
            error_msg = f"Failed to initialize LLM: {str(e)}"
            logger.error(error_msg)
            print(f"❌ LLM INIT ERROR: {error_msg}")
            state["terminate_signal"] = True
            if not state.get("messages"):
                state["messages"] = []
            state["messages"].append(
                AIMessage(content=f"❌ LLM Error: {error_msg}")
            )
            return state
        # Step 1: Sanity check
        try:
            print("🔍 Performing sanity check...")
            sanity_parser = JsonOutputParser(
                pydantic_object=RequestSanityCheckSchema
            )
            retry_parser = RetryOutputParser.from_llm(
                parser=sanity_parser, llm=llm
            )
            sanity_prompt = PromptTemplate(
                template=DA_SANITY_CHECK_TEMPLATE,
                input_variables=["query"],
                partial_variables={
                    "format_instructions": sanity_parser.get_format_instructions()
                },
            )
            sanity_chain = {
                "prompt": sanity_prompt,
                "llm_output": sanity_prompt | llm | StrOutputParser(),
            } | RunnableLambda(
                lambda x: retry_parser.parse_with_prompt(
                    x["llm_output"], x["prompt"]
                )
            )
            sanity_result = sanity_chain.invoke(
                {"query": state["current_user_prompt"]}
            )
            logger.debug(sanity_result)
            print(
                f"✅ Sanity check completed: {sanity_result.get('is_da_request', False)}"
            )

            if not sanity_result.get("is_da_request"):
                print("ℹ️ Request is not a data analysis request")
                return state
        except Exception as e:
            error_msg = f"Failed during sanity check: {str(e)}"
            logger.error(error_msg)
            print(f"❌ SANITY CHECK ERROR: {error_msg}")
            state["terminate_signal"] = True
            if not state.get("messages"):
                state["messages"] = []
            state["messages"].append(
                AIMessage(content=f"❌ Planning Error: {error_msg}")
            )
            return state

        # Step 2: Query rewriting
        try:
            print("✏️ Rewriting query...")
            rewrite_parser = JsonOutputParser(pydantic_object=RewriteSchema)
            retry_parser = RetryOutputParser.from_llm(
                parser=rewrite_parser, llm=llm
            )
            rewrite_prompt = PromptTemplate(
                template=DA_REWRITE_TEMPLATE,
                input_variables=["query"],
                partial_variables={
                    "format_instructions": rewrite_parser.get_format_instructions(),
                    "data_schema": data_for_planning.collect_schema(),
                },
            )
            rewrite_chain = {
                "prompt": rewrite_prompt,
                "llm_output": rewrite_prompt | llm | StrOutputParser(),
            } | RunnableLambda(
                lambda x: retry_parser.parse_with_prompt(
                    x["llm_output"], x["prompt"]
                )
            )
            rewrite_result = rewrite_chain.invoke(
                {"query": state["current_user_prompt"]}
            )
            logger.debug(rewrite_result)
            rewritten_query = rewrite_result.get("rewritten_query")
            print(f"✅ Query rewritten: {rewritten_query[:100]}...")
        except Exception as e:
            error_msg = f"Failed during query rewriting: {str(e)}"
            logger.error(error_msg)
            print(f"❌ QUERY REWRITE ERROR: {error_msg}")
            state["terminate_signal"] = True
            if not state.get("messages"):
                state["messages"] = []
            state["messages"].append(
                AIMessage(content=f"❌ Planning Error: {error_msg}")
            )
            return state

        # Step 3: Abstract planning
        try:
            print("📋 Creating abstract plan...")
            plan_prompt = PromptTemplate(
                template=ABSTRACT_PLAN_TEMPLATE,
                input_variables=["query"],
                partial_variables={
                    "data_description": describe(
                        data_for_planning, do_corr_analysis=False
                    )
                },
            )
            plan_chain = plan_prompt | llm
            plan_result_str = plan_chain.invoke({"query": rewritten_query})

            abstract_plan_parser = JsonOutputParser(
                pydantic_object=AbstractPlanSchema
            )
            retry_parser = RetryOutputParser.from_llm(
                parser=abstract_plan_parser, llm=llm
            )
            plan_parse_prompt = PromptTemplate(
                template=ABSTRACT_PLAN_PARSE_TEMPLATE,
                input_variables=["query"],
                partial_variables={
                    "format_instructions": abstract_plan_parser.get_format_instructions()
                },
            )
            plan_parse_chain = {
                "prompt": plan_parse_prompt,
                "llm_output": plan_parse_prompt | llm | StrOutputParser(),
            } | RunnableLambda(
                lambda x: retry_parser.parse_with_prompt(
                    x["llm_output"], x["prompt"]
                )
            )
            plan_result = plan_parse_chain.invoke({"query": plan_result_str})
            logger.debug(plan_result)

            state["abstract_plan_desc"] = json.dumps(plan_result)
            print("✅ Abstract plan created successfully")
        except Exception as e:
            error_msg = f"Failed during abstract planning: {str(e)}"
            logger.error(error_msg)
            print(f"❌ ABSTRACT PLANNING ERROR: {error_msg}")
            state["terminate_signal"] = True
            if not state.get("messages"):
                state["messages"] = []
            state["messages"].append(
                AIMessage(content=f"❌ Planning Error: {error_msg}")
            )
            return state

        print("✅ ABSTRACT_PLAN: Completed successfully")
        return state

    except Exception as e:
        error_msg = f"Unexpected error in abstract_plan node: {str(e)}"
        logger.error(error_msg)
        print(f"❌ ABSTRACT_PLAN SYSTEM ERROR: {error_msg}")

        # Print full traceback for debugging
        import traceback

        print(f"📋 Full traceback:")
        traceback.print_exc()

        # Ensure we have a messages list
        if not state.get("messages"):
            state["messages"] = []

        # Add error message and set termination signal
        state["messages"].append(
            AIMessage(content=f"❌ System Error in planning: {error_msg}")
        )
        state["terminate_signal"] = True
        return state


def data_engineering(state: AgentState) -> AgentState:
    # Get the first data source from state (already returns polars LazyFrame)
    first_data_source = state["loaded_data_sources"][0]
    data_object_lazy = deserialize_dataframe(first_data_source["data"])

    # <<< START OF YOUR ORIGINAL LOGIC >>>
    llm_pipeline = init_chat_model(
        "gpt-4.1",
        model_provider="openai",
        base_url="https://api.apiyi.com/v1",
        streaming=False,
        temperature=0,
        use_responses_api=False,
    )
    llm_code = init_chat_model(
        "gemini-2.5-pro",
        model_provider="openai",
        base_url="https://api.apiyi.com/v1",
        streaming=False,
    )

    pipeline_parser = JsonOutputParser(
        pydantic_object=DataProcessingPipelineSchema
    )
    retry_parser = RetryOutputParser.from_llm(
        parser=pipeline_parser, llm=llm_pipeline
    )
    pipeline_prompt = PromptTemplate(
        template=DE_PIPELINE_TEMPLATE,
        input_variables=["curation_hint"],
        partial_variables={
            "format_instructions": pipeline_parser.get_format_instructions(),
            "data_schema": data_object_lazy.collect_schema(),
        },
    )
    pipeline_chain = {
        "prompt": pipeline_prompt,
        "llm_output": pipeline_prompt | llm_pipeline | StrOutputParser(),
    } | RunnableLambda(
        lambda x: retry_parser.parse_with_prompt(x["llm_output"], x["prompt"])
    )
    pipeline_result = pipeline_chain.invoke(
        {
            "curation_hint": json.loads(state["abstract_plan_desc"])[
                "data_preparation_plan"
            ]
        }
    )

    data_object = data_object_lazy.collect().clone()

    # This logic is correct, it sequentially applies transformations
    if pipeline_result.get("handle_missing_values"):
        data_object = handle_missing_values(
            data_object, **pipeline_result["handle_missing_values"]
        )["df"]
    if pipeline_result.get("handle_outliers"):
        data_object = handle_outliers(
            data_object, **pipeline_result["handle_outliers"]
        )["df"]
    if pipeline_result.get("handle_duplicates"):
        data_object = handle_duplicates(
            data_object, **pipeline_result["handle_duplicates"]
        )["df"]
    if pipeline_result.get("perform_sorting"):
        data_object = perform_sorting(
            data_object, **pipeline_result["perform_sorting"]
        )["df"]
    if pipeline_result.get("perform_normalization"):
        data_object = perform_normalization(
            data_object, **pipeline_result["perform_normalization"]
        )["df"]

    # <<< END OF YOUR ORIGINAL LOGIC >>>

    if pipeline_result.get("further_curation"):
        # Implement robust code generation with retry and fallback
        curation_success = False
        max_retries = 3

        for attempt in range(max_retries):
            try:
                logger.info(
                    f"Code generation attempt {attempt + 1}/{max_retries}"
                )

                # Configure LLM with better parameters for code generation
                llm_code_robust = init_chat_model(
                    "gemini-2.5-pro",
                    model_provider="openai",
                    base_url="https://api.apiyi.com/v1",
                    streaming=False,
                    temperature=0.1,  # Low temperature for more deterministic code
                )

                curation_prompt = PromptTemplate(
                    template=DE_CODE_CURATION_TEMPLATE,
                    input_variables=["curation_hint"],
                    partial_variables={
                        "data_schema": data_object.collect_schema()
                    },
                )
                curation_chain = (
                    curation_prompt | llm_code_robust | StrOutputParser()
                )
                curation_result = curation_chain.invoke(
                    {"curation_hint": pipeline_result["further_curation"]}
                )
                curation_code = extract_python_code(curation_result)

                # Validate the generated code
                if not curation_code or len(curation_code.strip()) < 10:
                    logger.warning(
                        f"Generated code too short on attempt {attempt + 1}: '{curation_code}'"
                    )
                    continue

                logger.debug(
                    f"Generated code (attempt {attempt + 1}): {curation_code}"
                )

                # Try to execute the code
                outcome, df_refined, df_new = run_sandboxed_code(
                    curation_code, data_object.to_pandas()
                )

                # Check if execution was successful
                if (
                    outcome.get("status") == "success"
                    and df_refined is not None
                ):
                    logger.info(
                        f"Code generation and execution successful on attempt {attempt + 1}"
                    )
                    state["refined_data"] = serialize_dataframe(df_refined)
                    state["renewed_data"] = serialize_dataframe(df_new)
                    pprint(outcome)
                    curation_success = True
                    break
                else:
                    logger.warning(
                        f"Code execution failed on attempt {attempt + 1}: {outcome}"
                    )

            except Exception as e:
                logger.error(
                    f"Code generation attempt {attempt + 1} failed: {str(e)}"
                )
                continue

        # Fallback mechanism: Use uncurated data if all attempts fail
        if not curation_success:
            logger.warning(
                "All code generation attempts failed. Using uncurated data as fallback."
            )
            pandas_data_object = (
                data_object.to_pandas()
                if hasattr(data_object, "to_pandas")
                else data_object
            )
            state["refined_data"] = serialize_dataframe(pandas_data_object)
            state["renewed_data"] = None

            # Add a message to indicate fallback was used
            state["messages"].append(
                AIMessage(
                    content="⚠️ Data curation failed after multiple attempts. Proceeding with original data."
                )
            )
    else:
        # Convert polars to pandas before serializing
        pandas_data_object = (
            data_object.to_pandas()
            if hasattr(data_object, "to_pandas")
            else data_object
        )
        state["refined_data"] = serialize_dataframe(pandas_data_object)
        state["renewed_data"] = None

    logger.info("Data engineering complete. Data saved to state.")
    return state


def data_analysis(state: AgentState) -> AgentState:
    logger.info("--- Entering Data Analysis Node ---")

    # Load refined data from state
    if state.get("refined_data"):
        refined_df_lazy = deserialize_dataframe(state["refined_data"])
        # Convert to pandas for analysis tools (they expect pandas)
        refined_df = (
            refined_df_lazy.collect().to_pandas()
            if hasattr(refined_df_lazy, "collect")
            else refined_df_lazy
        )
    else:
        logger.warning(
            "Refined data not found in state. Skipping data analysis."
        )
        return state

    # Load renewed data if available
    renewed_df = None
    if state.get("renewed_data"):
        renewed_df_lazy = deserialize_dataframe(state["renewed_data"])
        # Convert to pandas for analysis tools (they expect pandas)
        renewed_df = (
            renewed_df_lazy.collect().to_pandas()
            if hasattr(renewed_df_lazy, "collect")
            else renewed_df_lazy
        )

    # Initialize LLM models
    llm_structured = init_chat_model(
        "gpt-4.1",
        model_provider="openai",
        base_url="https://api.apiyi.com/v1",
        streaming=False,
        temperature=0,
        use_responses_api=False,
    )

    llm_code = init_chat_model(
        "gemini-2.5-pro",
        model_provider="openai",
        base_url="https://api.apiyi.com/v1",
        streaming=False,
    )

    # Step 1: Generate descriptive analysis and add to messages
    logger.info("Step 1: Generating descriptive analysis")

    # Convert refined_df to polars LazyFrame for describe function
    if hasattr(refined_df, "lazy"):
        refined_lazy = refined_df.lazy()
    else:
        # Convert pandas to polars
        refined_lazy = pl.from_pandas(refined_df).lazy()

    refined_descriptive_results = describe(refined_lazy, do_corr_analysis=True)

    # Add renewed_df description if available
    if renewed_df is not None:
        if hasattr(renewed_df, "lazy"):
            renewed_lazy = renewed_df.lazy()
        else:
            renewed_lazy = pl.from_pandas(renewed_df).lazy()
        renewed_descriptive_results = describe(
            renewed_lazy, do_corr_analysis=True
        )

    # Add descriptive results to messages
    descriptive_message = (
        f"Refined Descriptive Analysis Results:\n{refined_descriptive_results}"
    )
    if renewed_df is not None:
        descriptive_message += f"\n\nRenewed Data Descriptive Analysis Results:\n{renewed_descriptive_results}"
    state["messages"].append(AIMessage(content=descriptive_message))

    # Step 2: Check if descriptive analysis is sufficient
    logger.info("Step 2: Checking if descriptive analysis is sufficient")

    sufficiency_parser = JsonOutputParser(
        pydantic_object=DescriptiveSufficiencyCheckSchema
    )
    retry_parser = RetryOutputParser.from_llm(
        parser=sufficiency_parser, llm=llm_structured
    )

    sufficiency_prompt = PromptTemplate(
        template=DA_DESCRIPTIVE_SUFFICIENCY_TEMPLATE,
        input_variables=["user_question", "descriptive_results"],
        partial_variables={
            "format_instructions": sufficiency_parser.get_format_instructions()
        },
    )

    sufficiency_chain = {
        "prompt": sufficiency_prompt,
        "llm_output": sufficiency_prompt | llm_structured | StrOutputParser(),
    } | RunnableLambda(
        lambda x: retry_parser.parse_with_prompt(x["llm_output"], x["prompt"])
    )

    sufficiency_result = sufficiency_chain.invoke(
        {
            "user_question": state["current_user_prompt"],
            "descriptive_results": descriptive_message,
        }
    )

    logger.debug(f"Sufficiency check result: {sufficiency_result}")

    if sufficiency_result.get("is_sufficient"):
        logger.info(
            "Descriptive analysis is sufficient. Proceeding to final answer generation."
        )
        # Jump to step 6 - final answer generation
        return _generate_final_answer(state, llm_structured)

    # Step 3: Retrieve relevant functions using RAG
    logger.info("Step 3: Retrieving relevant functions using RAG")

    data_analysis_plan = json.loads(state["abstract_plan_desc"])[
        "data_analysis_plan"
    ]

    # Initialize RAG system
    embed_func = CustomEmbeddingFunction.create(
        name="text-embedding-3-large",
        base_url="https://vip.apiyi.com/v1",
    )

    db_manager = DatabaseManager(embed_func)
    function_table = db_manager.create_or_update_database(
        "data_analysis", keyword_filters=["dev", "disabled"]
    )

    # Search for top 3 relevant functions
    if function_table and len(function_table):
        search_results = (
            function_table.search(data_analysis_plan)
            .limit(3)
            .to_pydantic(db_manager.schema)
        )

        available_functions = []
        for result in search_results:
            available_functions.append(
                {"name": result.name, "schema": json.loads(result.tool_schema)}
            )
    else:
        logger.warning("No functions found in RAG database")
        available_functions = []

    logger.debug(
        f"Retrieved functions: {[f['name'] for f in available_functions]}"
    )

    # Step 4: Execute retrieved functions
    if available_functions:
        logger.info("Step 4: Executing retrieved functions")

        for func_info in available_functions:
            try:
                # Determine function parameters using structured output
                function_parser = JsonOutputParser(
                    pydantic_object=FunctionExecutionSchema
                )
                retry_parser = RetryOutputParser.from_llm(
                    parser=function_parser, llm=llm_structured
                )

                function_prompt = PromptTemplate(
                    template=DA_FUNCTION_EXECUTION_TEMPLATE,
                    input_variables=[
                        "data_analysis_plan",
                        "available_functions",
                        "refined_df_schema",
                        "renewed_df_schema",
                    ],
                    partial_variables={
                        "format_instructions": function_parser.get_format_instructions()
                    },
                )

                # Get schemas for both dataframes
                refined_schema = (
                    str(refined_df.dtypes)
                    if hasattr(refined_df, "dtypes")
                    else str(refined_df.schema)
                )
                renewed_schema = (
                    str(renewed_df.dtypes)
                    if renewed_df is not None and hasattr(renewed_df, "dtypes")
                    else "None"
                )
                if renewed_df is not None and hasattr(renewed_df, "schema"):
                    renewed_schema = str(renewed_df.schema)

                function_chain = {
                    "prompt": function_prompt,
                    "llm_output": function_prompt
                    | llm_structured
                    | StrOutputParser(),
                } | RunnableLambda(
                    lambda x: retry_parser.parse_with_prompt(
                        x["llm_output"], x["prompt"]
                    )
                )

                function_result = function_chain.invoke(
                    {
                        "data_analysis_plan": data_analysis_plan,
                        "available_functions": json.dumps(
                            [func_info], indent=2
                        ),
                        "refined_df_schema": refined_schema,
                        "renewed_df_schema": renewed_schema,
                    }
                )

                logger.debug(f"Function execution plan: {function_result}")

                # Execute the function
                if function_result.get("function_name") == func_info["name"]:
                    target_df = (
                        refined_df
                        if function_result.get("target_dataframe")
                        == "refined_df"
                        else renewed_df
                    )
                    if target_df is not None:
                        # Get the actual function from REGISTERED_TOOLS
                        target_function = None
                        for tool in REGISTERED_TOOLS:
                            if tool.name == func_info["name"]:
                                target_function = tool.callable
                                break

                        if target_function:
                            # Execute function with parameters
                            # Remove 'df' from parameters since we pass it as first positional argument
                            parameters = function_result.get(
                                "parameters", {}
                            ).copy()
                            parameters.pop(
                                "df", None
                            )  # Remove df parameter if present

                            exec_result = target_function(
                                target_df,
                                **parameters,
                            )

                            # Add result to messages
                            result_message = f"Function {func_info['name']} execution result:\n{json.dumps(exec_result, indent=2, default=str)}"
                            state["messages"].append(
                                AIMessage(content=result_message)
                            )

                            logger.info(
                                f"Successfully executed function: {func_info['name']}"
                            )
                        else:
                            logger.warning(
                                f"Function {func_info['name']} not found in REGISTERED_TOOLS"
                            )
                    else:
                        logger.warning(
                            f"Target dataframe {function_result.get('target_dataframe')} is None"
                        )

            except Exception as e:
                logger.error(
                    f"Error executing function {func_info['name']}: {str(e)}"
                )
                continue

        # Step 5: Check if we now have enough information
        logger.info("Step 5: Checking if function results are sufficient")

        sufficiency_result_2 = sufficiency_chain.invoke(
            {
                "user_question": state["current_user_prompt"],
                "descriptive_results": "\n".join(
                    [
                        msg.content
                        for msg in state["messages"]
                        if isinstance(msg, AIMessage)
                    ]
                ),
            }
        )

        if sufficiency_result_2.get("is_sufficient"):
            logger.info(
                "Function execution results are sufficient. Proceeding to final answer generation."
            )
            return _generate_final_answer(state, llm_structured)

    # Fallback: Code generation
    logger.info("Step 5 (Fallback): Generating custom analysis code")

    code_prompt = PromptTemplate(
        template=DA_CODE_GENERATION_TEMPLATE,
        input_variables=[
            "data_analysis_plan",
            "refined_df_schema",
            "renewed_df_schema",
        ],
    )

    refined_schema = (
        str(refined_df.dtypes)
        if hasattr(refined_df, "dtypes")
        else str(refined_df.schema)
    )
    renewed_schema = (
        str(renewed_df.dtypes)
        if renewed_df is not None and hasattr(renewed_df, "dtypes")
        else "None"
    )
    if renewed_df is not None and hasattr(renewed_df, "schema"):
        renewed_schema = str(renewed_df.schema)

    code_chain = code_prompt | llm_code | StrOutputParser()
    generated_code = code_chain.invoke(
        {
            "data_analysis_plan": data_analysis_plan,
            "refined_df_schema": refined_schema,
            "renewed_df_schema": renewed_schema,
        }
    )

    # Extract and execute the code
    extracted_code = extract_python_code(generated_code)
    logger.debug(f"Generated code: {extracted_code}")

    if extracted_code:
        # Use the refined run_sandboxed_code function
        outcome, _, _ = run_sandboxed_code(
            extracted_code,
            refined_df=refined_df,
            renewed_df=renewed_df,
        )

        if outcome["status"] == "success":
            # Add result to messages
            code_result_message = f"Custom analysis code execution result:\n{outcome['execution_result']}"
            if outcome.get("message"):
                code_result_message += (
                    f"\n\nExecution summary:\n{outcome['message']}"
                )
            state["messages"].append(AIMessage(content=code_result_message))

            logger.info("Successfully executed custom analysis code")
        else:
            logger.error(
                f"Error executing custom analysis code: {outcome['message']}"
            )
            error_message = f"Error in custom analysis: {outcome['message']}"
            if outcome.get("traceback"):
                error_message += f"\n\nTraceback:\n{outcome['traceback']}"
            state["messages"].append(AIMessage(content=error_message))
    else:
        logger.warning("No code extracted from generated response")
        error_message = "No valid Python code could be extracted from the generated response"
        state["messages"].append(AIMessage(content=error_message))

    # Step 6: Generate final answer
    logger.info("Step 6: Generating final answer")
    return _generate_final_answer(state, llm_structured)


def _generate_final_answer(state: AgentState, llm_structured) -> AgentState:
    """Helper function to generate the final answer based on all analysis results."""
    logger.info("Generating final answer based on all analysis results")

    # Prepare all analysis results from messages
    analysis_results = []
    for msg in state["messages"]:
        if isinstance(msg, AIMessage):
            analysis_results.append(msg.content)

    # Generate final answer
    final_answer_parser = JsonOutputParser(pydantic_object=FinalAnswerSchema)
    retry_parser = RetryOutputParser.from_llm(
        parser=final_answer_parser, llm=llm_structured
    )

    final_answer_prompt = PromptTemplate(
        template=DA_FINAL_ANSWER_TEMPLATE,
        input_variables=["user_question", "analysis_results"],
        partial_variables={
            "format_instructions": final_answer_parser.get_format_instructions()
        },
    )

    final_answer_chain = {
        "prompt": final_answer_prompt,
        "llm_output": final_answer_prompt | llm_structured | StrOutputParser(),
    } | RunnableLambda(
        lambda x: retry_parser.parse_with_prompt(x["llm_output"], x["prompt"])
    )

    final_answer_result = final_answer_chain.invoke(
        {
            "user_question": state["current_user_prompt"],
            "analysis_results": "\n\n".join(analysis_results),
        }
    )

    # Store the final answer in the state
    state["summary_report"] = final_answer_result

    # Add final answer to messages
    final_message = (
        f"Final Answer:\n{final_answer_result.get('answer', '')}\n\nKey Findings:\n"
        + "\n".join(
            [
                f"- {finding}"
                for finding in final_answer_result.get("key_findings", [])
            ]
        )
    )
    state["messages"].append(AIMessage(content=final_message))

    logger.info("Final answer generated successfully")
    logger.info(f"Final answer: {final_message}")
    return state


# --- Graph Definition ---
builder = StateGraph(AgentState)
builder.add_node("standby", standby)
builder.add_node("abstract_plan", abstract_plan)
builder.add_node("data_engineering", data_engineering)
builder.add_node("data_analysis", data_analysis)
builder.add_edge(START, "standby")
builder.add_conditional_edges("standby", standby_routing)
builder.add_edge("abstract_plan", "data_engineering")
builder.add_edge("data_engineering", "data_analysis")
builder.add_edge("data_analysis", END)

# --- Main Execution Block ---
if __name__ == "__main__":
    print(REGISTERED_TOOLS)
    print("--------------------------------")

    conn = sqlite3.connect("cache.db", check_same_thread=False)
    try:
        checkpointer = SqliteSaver(conn=conn)

        # Option to force data_analysis re-execution
        FORCE_DATA_ANALYSIS = True  # Set to True to force re-run data_analysis

        if FORCE_DATA_ANALYSIS:
            graph = builder.compile(
                checkpointer=checkpointer, interrupt_before=["data_analysis"]
            )
        else:
            graph = builder.compile(
                checkpointer=checkpointer,
            )

        # --- Configuration ---
        query = "How does the shift duration change over time?"
        file_path = "../../../test/shift.csv"
        input_string = f"{query}|{file_path}"
        thread_id = hashlib.sha256(input_string.encode()).hexdigest()
        config = {"configurable": {"thread_id": thread_id}}

        # --- Check for existing state to decide whether to start or resume ---
        existing_state = checkpointer.get(config)

        final_result = None
        if existing_state is None:
            # FIRST RUN: No state exists, so we start a new run with the initial input.
            print(
                f"--- No checkpoint found for thread '{thread_id}'. Starting new run. ---"
            )
            initial_input = {
                "current_user_prompt": query,
                "target_data_sources": [file_path],
            }
            # Use .stream() to see each step
            for chunk in graph.stream(initial_input, config=config):
                # A chunk is a dictionary with the node name as the key
                node_name = list(chunk.keys())[0]
                pprint({node_name: chunk[node_name]})
                final_result = chunk

        else:
            # SECOND RUN: State exists, so we resume from where we left off.
            print(
                f"--- Checkpoint found for thread '{thread_id}'. Resuming run. ---"
            )

            # Debug: Show current state
            current_state = existing_state["channel_values"]
            print(f"Current state keys: {list(current_state.keys())}")
            print(
                f"Has refined_data_path: {'refined_data_path' in current_state}"
            )
            print(
                f"Has renewed_data_path: {'renewed_data_path' in current_state}"
            )
            if "refined_data_path" in current_state:
                print(
                    f"refined_data_path: {current_state['refined_data_path']}"
                )
            if "renewed_data_path" in current_state:
                print(
                    f"renewed_data_path: {current_state['renewed_data_path']}"
                )

            # Use .stream() with None to see each step during resumption
            chunk_count = 0
            for chunk in graph.stream(None, config=config):
                chunk_count += 1
                node_name = list(chunk.keys())[0]
                print(f"Processing chunk {chunk_count}: {node_name}")
                final_result = chunk

            if chunk_count == 0:
                print(
                    "No chunks to process - execution may have already completed."
                )
                # Try to get the final state
                final_state = checkpointer.get(config)
                if final_state:
                    print("Final state exists. Checking last executed node...")

                    # If FORCE_DATA_ANALYSIS is True and we're interrupting before data_analysis,
                    # manually call the data_analysis function
                    if FORCE_DATA_ANALYSIS:
                        print(
                            "FORCE_DATA_ANALYSIS is enabled. Running data_analysis manually..."
                        )
                        try:
                            result_state = data_analysis(current_state)
                            print("data_analysis completed successfully!")
                            final_result = {"data_analysis": result_state}
                        except Exception as e:
                            print(f"Error running data_analysis: {e}")
                            import traceback

                            traceback.print_exc()

        print("\n--- Execution Complete ---")
        if final_result:
            # The final result is the output of the last node that ran
            last_node = list(final_result.keys())[0]
            if last_node == "data_analysis":
                print("Successfully resumed and ran the 'data_analysis' node.")
            else:
                print(f"Execution stopped at node '{last_node}'.")

    finally:
        conn.close()
        conn.close()
