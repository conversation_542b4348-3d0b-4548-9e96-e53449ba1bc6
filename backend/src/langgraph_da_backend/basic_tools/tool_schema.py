import json
from typing import Annotated, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field

# --- Strategy Enums ---


MissValuesStrategy = Literal[
    "drop_col",
    "drop_rows",
    "mean",
    "median",
    "mode",
    "forward",
    "backward",
    "constant",
]
OutliersStrategy = Literal["winsorize", "remove"]
NormStrategy = Literal["min_max", "z_score"]


# --- Parameter Models ---


class HandleMissValuesParams(BaseModel):
    """Parameters for handling missing values in specified columns."""

    strategies: Dict[str, MissValuesStrategy] = Field(
        ...,
        description="A dictionary mapping column names to the desired imputation strategy. ",
    )
    fill_values: Dict[str, Union[int, float, str]] = Field(
        {},
        description="A dictionary mapping a column name to its constant fill value. This is ONLY used if the strategy for that column is 'constant'.",
    )


class HandleOutliersParams(BaseModel):
    """Parameters for handling outliers in specified columns."""

    strategies: Dict[str, OutliersStrategy] = Field(
        ...,
        description="A dictionary mapping column names to the desired outlier handling strategy.",
    )


class HandleDuplicatesParams(BaseModel):
    """Parameters for handling duplicate rows."""

    col_names: List[str] = Field(
        ...,
        description="List of column names to check for duplicates. ",
    )


class SortParams(BaseModel):
    """Parameters for sorting the dataset."""

    col_names: List[str] = Field(
        ...,
        description="List of column names to sort by. The order of columns in this list defines the sorting priority.",
    )
    descending: List[bool] = Field(
        ...,
        description="List of booleans, corresponding to each column in `col_names`. `True` for descending, `False` for ascending.",
    )


class NormParams(BaseModel):
    """Parameters for normalizing numerical columns."""

    strategies: Dict[str, NormStrategy] = Field(
        ...,
        description="A dictionary mapping column names to the desired normalization strategy. ",
    )


class CurateParams(BaseModel):
    """Parameters for arbitrary dataframe curating."""

    methodology: Optional[str] = Field(
        ...,
        description="A brief description of any necessary further curation.",
    )


class DataProcessingPipeline(BaseModel):
    """
    Defines a fixed, sequential pipeline for data processing.
    Each field represents a distinct step in the pipeline.
    The LLM's task is to provide the appropriate parameters for each step it deems necessary based on the data context.
    To skip a step entirely, provide `null` as the field value.
    The steps will be executed in the order: `handle_missing_values` -> `handle_outliers` -> `handle_duplicates` -> `perform_sorting` -> `perform_normalization`.
    If the above steps are not sufficient, an extra `further_curation` will be performed manually.
    """

    handle_missing_values: Optional[HandleMissValuesParams] = Field(
        default=None,
        description="Configuration for the missing value handling step.",
    )
    handle_outliers: Optional[HandleOutliersParams] = Field(
        default=None, description="Configuration for the outlier handling step."
    )
    handle_duplicates: Optional[HandleDuplicatesParams] = Field(
        default=None,
        description="Configuration for the duplicate handling step.",
    )
    perform_sorting: Optional[SortParams] = Field(
        default=None, description="Configuration for the sorting step."
    )
    perform_normalization: Optional[NormParams] = Field(
        default=None,
        description="Configuration for the data normalization step.",
    )
    further_curation: Optional[CurateParams] = Field(
        default=None,
        description="Description for any necessary further curation.",
    )
