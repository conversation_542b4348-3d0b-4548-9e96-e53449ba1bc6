# mypy: disable-error-code="no-untyped-def,misc"
import asyncio
import json
import os
import pathlib
import uuid
from typing import Any, Dict, List

from dotenv import load_dotenv
from fastapi import (
    FastAPI,
    File,
    HTTPException,
    Response,
    UploadFile,
    WebSocket,
    WebSocketDisconnect,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

# Load environment variables from .env file
load_dotenv()

# Explicitly disable <PERSON><PERSON><PERSON> tracing for open-source compatibility
os.environ.setdefault("LANGCHAIN_TRACING_V2", "false")

# Import our graph
from .graph import graph


def validate_environment():
    """Validate required environment variables and configuration."""
    missing_vars = []

    # Check for required environment variables
    required_vars = ["OPENAI_API_KEY"]
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        error_msg = (
            f"Missing required environment variables: {', '.join(missing_vars)}"
        )
        print(f"❌ CONFIGURATION ERROR: {error_msg}")
        print("💡 Please ensure the following environment variables are set:")
        for var in missing_vars:
            print(f"   - {var}")
        print("💡 You can set these in a .env file in the backend directory")
        raise EnvironmentError(error_msg)

    print("✅ Environment validation passed")
    return True


# Validate environment on startup
try:
    validate_environment()
except EnvironmentError as e:
    print(f"❌ Startup failed: {e}")
    print("🛑 Server cannot start without proper configuration")
    # Don't exit here, let FastAPI handle the startup
    pass

# Define the FastAPI app
app = FastAPI()

# Add CORS middleware for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Pydantic models
class ThreadCreate(BaseModel):
    pass


class RunCreate(BaseModel):
    assistant_id: str
    input: Dict[str, Any]
    config: Dict[str, Any] = {}


class ChatMessage(BaseModel):
    role: str
    content: str
    id: str = None


class ChatRequest(BaseModel):
    message: str
    file_path: str = None
    thread_id: str = None


# Store active WebSocket connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.thread_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, thread_id: str = None):
        await websocket.accept()
        self.active_connections.append(websocket)
        if thread_id:
            if thread_id not in self.thread_connections:
                self.thread_connections[thread_id] = []
            self.thread_connections[thread_id].append(websocket)

    def disconnect(self, websocket: WebSocket, thread_id: str = None):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if thread_id and thread_id in self.thread_connections:
            if websocket in self.thread_connections[thread_id]:
                self.thread_connections[thread_id].remove(websocket)
            if not self.thread_connections[thread_id]:
                del self.thread_connections[thread_id]

    async def send_to_thread(self, thread_id: str, message: dict):
        if thread_id in self.thread_connections:
            for connection in self.thread_connections[thread_id]:
                try:
                    await connection.send_text(json.dumps(message))
                except:
                    # Remove dead connections
                    self.disconnect(connection, thread_id)


manager = ConnectionManager()


# API endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "message": "Data Analysis LangGraph backend is running",
    }


@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload a CSV file and return the server file path."""
    try:
        # Validate file type
        if not file.filename.lower().endswith(".csv"):
            raise HTTPException(
                status_code=400, detail="Only CSV files are allowed"
            )

        # Create uploads directory if it doesn't exist
        uploads_dir = pathlib.Path("uploads")
        uploads_dir.mkdir(exist_ok=True)

        # Generate unique filename to avoid conflicts
        file_id = str(uuid.uuid4())
        file_extension = pathlib.Path(file.filename).suffix
        server_filename = f"{file_id}{file_extension}"
        file_path = uploads_dir / server_filename

        # Save the uploaded file
        print(f"📁 Uploading file: {file.filename} -> {file_path}")
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # Return the server file path
        absolute_path = file_path.resolve().as_posix()
        print(f"✅ File uploaded successfully: {absolute_path}")

        return {
            "success": True,
            "file_path": absolute_path,
            "original_filename": file.filename,
            "server_filename": server_filename,
            "file_size": len(content),
        }

    except Exception as e:
        print(f"❌ File upload error: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"File upload failed: {str(e)}"
        )


@app.get("/assistants")
async def list_assistants():
    """List available assistants."""
    return [{"assistant_id": "agent", "name": "Data Analysis Agent"}]


@app.post("/threads")
async def create_thread():
    """Create a new thread."""
    thread_id = str(uuid.uuid4())
    return {"thread_id": thread_id}


@app.get("/threads")
async def list_threads():
    """List threads."""
    return []


@app.post("/threads/{thread_id}/runs")
async def create_run(thread_id: str, run_data: RunCreate):
    """Create and execute a run."""
    try:
        config = {"configurable": {"thread_id": thread_id}}
        config.update(run_data.config)

        # Execute the graph
        result = graph.invoke(run_data.input, config=config)

        run_id = str(uuid.uuid4())
        return {
            "run_id": run_id,
            "thread_id": thread_id,
            "assistant_id": run_data.assistant_id,
            "status": "completed",
            "output": result,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/threads/{thread_id}/runs/stream")
async def stream_run(thread_id: str, run_data: RunCreate):
    """Stream a run execution."""
    try:
        config = {"configurable": {"thread_id": thread_id}}
        config.update(run_data.config)

        def generate_stream():
            try:
                run_id = str(uuid.uuid4())

                # Send metadata
                yield f"data: {json.dumps({'event': 'metadata', 'data': {'run_id': run_id, 'thread_id': thread_id}})}\n\n"

                # Stream the graph execution with proper sync handling
                import asyncio

                def run_graph_sync():
                    """Run the graph synchronously and collect all chunks."""
                    chunks = []
                    for chunk in graph.stream(
                        run_data.input, config=config, stream_mode="values"
                    ):
                        chunks.append(chunk)
                    return chunks

                # Execute the graph synchronously to avoid truncation
                chunks = run_graph_sync()

                # Yield chunks sequentially
                for chunk in chunks:
                    event_data = {"event": "values", "data": chunk}
                    yield f"data: {json.dumps(event_data)}\n\n"

                # Send completion
                yield f"data: {json.dumps({'event': 'end', 'data': {'run_id': run_id, 'status': 'completed'}})}\n\n"

            except Exception as e:
                error_data = {"event": "error", "data": {"error": str(e)}}
                yield f"data: {json.dumps(error_data)}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            },
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.websocket("/ws/{thread_id}")
async def websocket_endpoint(websocket: WebSocket, thread_id: str):
    """WebSocket endpoint for real-time communication."""
    await manager.connect(websocket, thread_id)
    try:
        while True:
            # Wait for messages from the client
            data = await websocket.receive_text()
            print(f"📨 WebSocket received data: {data}")
            message_data = json.loads(data)
            print(f"📨 Parsed message data: {message_data}")

            # Handle different message types
            if message_data.get("type") == "chat":
                print(f"🔄 Handling chat message for thread {thread_id}")
                await handle_chat_message(thread_id, message_data, websocket)
            elif message_data.get("type") == "get_history":
                await send_history(thread_id, websocket)

    except WebSocketDisconnect:
        manager.disconnect(websocket, thread_id)
    except Exception as e:
        print(f"WebSocket error: {e}")
        manager.disconnect(websocket, thread_id)


async def handle_chat_message(
    thread_id: str, message_data: dict, websocket: WebSocket
):
    """Handle incoming chat messages and stream responses."""
    try:
        # Send acknowledgment
        await websocket.send_text(
            json.dumps(
                {
                    "type": "status",
                    "status": "processing",
                    "message": "Processing your request...",
                }
            )
        )

        # Check for required environment variables
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            await websocket.send_text(
                json.dumps(
                    {
                        "type": "error",
                        "error": "OPENAI_API_KEY environment variable is not set. Please set your OpenAI API key to use this application.",
                        "error_type": "ConfigurationError",
                        "message": "Missing API key configuration",
                    }
                )
            )
            return

        # Prepare the input for the graph
        user_message = message_data.get("message", "")
        file_path = message_data.get("file_path")

        print(f"💬 Processing message: {user_message}")
        print(f"📁 File path: {file_path}")

        # Validate file path if provided
        if file_path:
            file_exists = os.path.exists(file_path)
            print(f"📋 File exists check: {file_exists}")
            if not file_exists:
                error_msg = f"File not found at path: {file_path}"
                print(f"❌ {error_msg}")
                await websocket.send_text(
                    json.dumps(
                        {
                            "type": "error",
                            "error": error_msg,
                            "error_type": "FileNotFoundError",
                            "message": "File upload issue",
                            "details": "The uploaded file could not be found on the server.",
                            "troubleshooting": [
                                "Try uploading the file again",
                                "Check if the file is a valid CSV format",
                                "Ensure the file is not corrupted",
                            ],
                        }
                    )
                )
                return

        config = {"configurable": {"thread_id": thread_id}}

        # Create the input based on your graph's expected format
        # Include all required fields from AgentState
        graph_input = {
            "current_user_prompt": user_message,
            "target_data_sources": [file_path] if file_path else [],
            "terminate_signal": False,
            "loaded_data_paths": [],
            "refined_data_path": None,
            "renewed_data_path": None,
            "abstract_plan_desc": "",
            "abstract_plan_feedback": "",
            "reflection_hint": "",
            "tool_exec_results": [],
            "execution_reflection_feedback": "",
            "summary_report": None,
        }

        # Send user message to client
        await websocket.send_text(
            json.dumps(
                {
                    "type": "message",
                    "role": "user",
                    "content": user_message,
                    "id": str(uuid.uuid4()),
                }
            )
        )

        # Stream the graph execution
        ai_message_id = str(uuid.uuid4())
        ai_content = ""

        try:
            # Send initial status
            await websocket.send_text(
                json.dumps(
                    {
                        "type": "status",
                        "status": "starting",
                        "message": "Starting graph execution...",
                    }
                )
            )

            chunk_count = 0
            print(f"🚀 Starting graph execution with input: {graph_input}")

            # Use asyncio to properly handle the synchronous graph execution
            import asyncio

            # Run the graph in a thread pool to avoid blocking async operations
            loop = asyncio.get_event_loop()

            def run_graph_sync():
                """Run the graph synchronously and collect all chunks."""
                chunks = []
                try:
                    print(f"🚀 Starting synchronous graph execution...")
                    for i, chunk in enumerate(
                        graph.stream(
                            graph_input, config=config, stream_mode="values"
                        )
                    ):
                        print(f"📦 Collected chunk {i+1}: {list(chunk.keys())}")
                        chunks.append(chunk)
                    print(
                        f"✅ Graph execution completed. Collected {len(chunks)} chunks."
                    )
                except Exception as e:
                    print(f"❌ Error during graph execution: {str(e)}")
                    import traceback

                    traceback.print_exc()
                    raise
                return chunks

            # Execute the graph in a thread pool with timeout
            try:
                chunks = await asyncio.wait_for(
                    loop.run_in_executor(None, run_graph_sync),
                    timeout=300,  # 5 minute timeout
                )
            except asyncio.TimeoutError:
                print("❌ Graph execution timed out after 5 minutes")
                await websocket.send_text(
                    json.dumps(
                        {
                            "type": "error",
                            "error": "Graph execution timed out after 5 minutes",
                            "error_type": "TimeoutError",
                            "message": "The analysis is taking too long. Please try with a smaller dataset or simpler query.",
                        }
                    )
                )
                return

            # Process chunks sequentially to maintain order
            for chunk_count, chunk in enumerate(chunks, 1):
                print(
                    f"📊 Processing chunk {chunk_count}: {list(chunk.keys())}"
                )

                # Log more details about the chunk
                for key, value in chunk.items():
                    if key == "messages":
                        print(
                            f"  📝 Messages: {len(value) if value else 0} messages"
                        )
                    elif key in ["loaded_data_paths", "target_data_sources"]:
                        print(f"  📁 {key}: {value}")
                    elif key == "terminate_signal":
                        print(f"  🛑 Terminate signal: {value}")
                    elif key == "current_user_prompt":
                        print(
                            f"  💬 User prompt: {value[:50]}..."
                            if len(str(value)) > 50
                            else f"  💬 User prompt: {value}"
                        )
                    else:
                        print(
                            f"  🔧 {key}: {str(value)[:100]}..."
                            if len(str(value)) > 100
                            else f"  🔧 {key}: {value}"
                        )

                # Send progress updates (serialize LangChain objects)
                serialized_chunk = {}
                for key, value in chunk.items():
                    if key == "messages":
                        # Convert LangChain messages to serializable format
                        serialized_chunk[key] = [
                            {
                                "role": (
                                    msg.type
                                    if hasattr(msg, "type")
                                    else "unknown"
                                ),
                                "content": (
                                    msg.content
                                    if hasattr(msg, "content")
                                    else str(msg)
                                ),
                                "id": str(uuid.uuid4()),
                            }
                            for msg in value
                        ]
                    else:
                        # For other fields, try to serialize or convert to string
                        try:
                            json.dumps(value)  # Test if it's JSON serializable
                            serialized_chunk[key] = value
                        except (TypeError, ValueError):
                            serialized_chunk[key] = str(value)

                # Send progress update
                await websocket.send_text(
                    json.dumps(
                        {
                            "type": "progress",
                            "data": serialized_chunk,
                            "chunk_number": chunk_count,
                        }
                    )
                )

                # If there are messages in the chunk, extract AI responses
                if "messages" in chunk:
                    messages = chunk["messages"]
                    for msg in messages:
                        if hasattr(msg, "type") and msg.type == "ai":
                            ai_content = msg.content

            print(f"✅ Graph execution completed after {chunk_count} chunks")

            # Send final AI message if we have one
            if ai_content:
                await websocket.send_text(
                    json.dumps(
                        {
                            "type": "message",
                            "role": "ai",
                            "content": ai_content,
                            "id": ai_message_id,
                        }
                    )
                )

            # Send completion status
            await websocket.send_text(
                json.dumps(
                    {
                        "type": "status",
                        "status": "completed",
                        "message": f"Analysis completed successfully after {chunk_count} steps",
                    }
                )
            )

        except Exception as e:
            error_msg = str(e)
            error_type = type(e).__name__
            print(f"❌ Graph execution error ({error_type}): {error_msg}")

            # Print full traceback for debugging
            import traceback

            print(f"📋 Full traceback:")
            traceback.print_exc()

            # Determine specific error messages based on error type
            user_friendly_msg = error_msg
            troubleshooting_tips = []

            if "OPENAI_API_KEY" in error_msg or "API key" in error_msg:
                user_friendly_msg = "OpenAI API key is missing or invalid. Please check your environment configuration."
                troubleshooting_tips = [
                    "Verify OPENAI_API_KEY is set in your .env file",
                    "Check if your API key is valid and has sufficient credits",
                    "Restart the backend server after updating the .env file",
                ]
            elif "Connection" in error_msg or "timeout" in error_msg.lower():
                user_friendly_msg = (
                    "Network connection error. Unable to reach the AI service."
                )
                troubleshooting_tips = [
                    "Check your internet connection",
                    "Verify the API endpoint is accessible",
                    "Try again in a few moments",
                ]
            elif (
                "File not found" in error_msg
                or "FileNotFoundError" in error_type
            ):
                user_friendly_msg = (
                    "The specified data file could not be found."
                )
                troubleshooting_tips = [
                    "Check if the file path is correct",
                    "Ensure the file exists and is accessible",
                    "Try uploading the file again",
                ]
            elif "JSON" in error_msg or "parse" in error_msg.lower():
                user_friendly_msg = "Error processing AI response. The AI service returned an unexpected format."
                troubleshooting_tips = [
                    "This is usually a temporary issue",
                    "Try submitting your request again",
                    "If the problem persists, try simplifying your question",
                ]

            # Send detailed error information
            await websocket.send_text(
                json.dumps(
                    {
                        "type": "error",
                        "error": user_friendly_msg,
                        "error_type": error_type,
                        "message": f"Analysis failed: {error_type}",
                        "details": f"Error occurred during analysis execution. Check backend logs for technical details.",
                        "troubleshooting": troubleshooting_tips,
                        "technical_error": error_msg,  # Include technical details for debugging
                    }
                )
            )

    except Exception as e:
        error_msg = str(e)
        error_type = type(e).__name__
        print(
            f"❌ WebSocket message processing error ({error_type}): {error_msg}"
        )

        # Print full traceback for debugging
        import traceback

        print(f"📋 Full traceback:")
        traceback.print_exc()

        try:
            await websocket.send_text(
                json.dumps(
                    {
                        "type": "error",
                        "error": f"Failed to process message: {error_msg}",
                        "error_type": error_type,
                        "message": "Message processing failed",
                        "details": "An unexpected error occurred while processing your request.",
                    }
                )
            )
        except Exception as send_error:
            print(f"❌ Failed to send error message to client: {send_error}")


async def send_history(thread_id: str, websocket: WebSocket):
    """Send conversation history to the client."""
    try:
        config = {"configurable": {"thread_id": thread_id}}
        state = graph.get_state(config)

        messages = []
        if state and state.values and "messages" in state.values:
            for msg in state.values["messages"]:
                if hasattr(msg, "type") and hasattr(msg, "content"):
                    # Convert LangChain message type to our format
                    role = "user" if msg.type == "human" else "ai"
                    messages.append(
                        {
                            "role": role,
                            "content": msg.content,
                            "id": str(uuid.uuid4()),
                        }
                    )

        await websocket.send_text(
            json.dumps({"type": "history", "messages": messages})
        )

    except Exception as e:
        await websocket.send_text(
            json.dumps(
                {"type": "error", "error": f"Failed to get history: {str(e)}"}
            )
        )


@app.post("/threads/{thread_id}/history")
async def get_thread_history(thread_id: str):
    """Get the history of a thread (REST endpoint for compatibility)."""
    try:
        config = {"configurable": {"thread_id": thread_id}}
        state = graph.get_state(config)

        messages = []
        if state and state.values and "messages" in state.values:
            for msg in state.values["messages"]:
                if hasattr(msg, "type") and hasattr(msg, "content"):
                    # Convert LangChain message type to our format
                    role = "user" if msg.type == "human" else "ai"
                    messages.append(
                        {
                            "role": role,
                            "content": msg.content,
                            "id": str(uuid.uuid4()),
                        }
                    )

        return messages

    except Exception as e:
        print(f"Warning: Could not get state for thread {thread_id}: {e}")
        return []


def create_frontend_router(build_dir="../../../frontend/dist"):
    """Creates a router to serve the React frontend.

    Args:
        build_dir: Path to the React build directory relative to this file.

    Returns:
        A Starlette application serving the frontend.
    """
    build_path = pathlib.Path(__file__).parent.parent.parent / build_dir

    if not build_path.is_dir() or not (build_path / "index.html").is_file():
        print(
            f"WARN: Frontend build directory not found or incomplete at {build_path}. Serving frontend will likely fail."
        )
        # Return a dummy router if build isn't ready
        from starlette.routing import Route

        async def dummy_frontend(_):
            return Response(
                "Frontend not built. Run 'npm run build' in the frontend directory.",
                media_type="text/plain",
                status_code=503,
            )

        return Route("/{path:path}", endpoint=dummy_frontend)

    return StaticFiles(directory=build_path, html=True)


# Mount the frontend under /app to not conflict with the LangGraph API routes
app.mount(
    "/app",
    create_frontend_router(),
    name="frontend",
)
