"""
LangGraph workflow definition for the Data Analysis backend.
This module exposes the graph that will be served by LangGraph Studio.
"""

from pathlib import Path

from langgraph.checkpoint.sqlite import SqliteSaver
from langgraph.graph import END, START, StateGraph

# Import the components from dummy_agent.py
from .dummy_agent import (
    AgentState,
    abstract_plan,
    data_analysis,
    data_engineering,
    standby,
)

# Create the checkpointer for state persistence
CHECKPOINT_DB_PATH = Path(".checkpoints/checkpoints.db")
CHECKPOINT_DB_PATH.parent.mkdir(exist_ok=True)

# Create the checkpointer properly
import sqlite3

conn = sqlite3.connect(str(CHECKPOINT_DB_PATH), check_same_thread=False)
checkpointer = SqliteSaver(conn)

# Build the graph
builder = StateGraph(AgentState)

# Add nodes
builder.add_node("standby", standby)
builder.add_node("abstract_plan", abstract_plan)
builder.add_node("data_engineering", data_engineering)
builder.add_node("data_analysis", data_analysis)

# Add edges
builder.add_edge(START, "standby")
builder.add_conditional_edges(
    "standby",
    lambda state: "abstract_plan" if not state.get("terminate_signal") else END,
)
builder.add_conditional_edges(
    "abstract_plan",
    lambda state: (
        "data_engineering" if not state.get("terminate_signal") else END
    ),
)
builder.add_conditional_edges(
    "data_engineering",
    lambda state: "data_analysis" if not state.get("terminate_signal") else END,
)
builder.add_edge("data_analysis", END)

# Compile the graph
graph = builder.compile(checkpointer=checkpointer)
