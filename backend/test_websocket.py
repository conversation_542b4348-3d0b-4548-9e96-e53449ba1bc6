#!/usr/bin/env python3
"""
Simple test script to verify WebSocket functionality.
"""

import asyncio
import json

import websockets


async def test_websocket():
    uri = "ws://localhost:2025/ws/test-thread-123"

    try:
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to WebSocket")

            # Test getting history
            await websocket.send(json.dumps({"type": "get_history"}))
            response = await websocket.recv()
            data = json.loads(response)
            print(f"📜 History response: {data}")

            # Test sending a chat message
            chat_message = {
                "type": "chat",
                "message": "Analyze the data and show me the average salary",
                "file_path": "/home/<USER>/Project/da-langgraph/backend/test_data.csv",
            }
            await websocket.send(json.dumps(chat_message))
            print("📤 Sent chat message")

            # Listen for responses
            for i in range(5):  # Listen for up to 5 messages
                try:
                    response = await asyncio.wait_for(
                        websocket.recv(), timeout=2.0
                    )
                    data = json.loads(response)
                    print(f"📥 Received: {data['type']} - {data}")
                except asyncio.TimeoutError:
                    print("⏰ Timeout waiting for response")
                    break
                except Exception as e:
                    print(f"❌ Error receiving message: {e}")
                    break

    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")


if __name__ == "__main__":
    print("🧪 Testing WebSocket connection...")
    asyncio.run(test_websocket())
