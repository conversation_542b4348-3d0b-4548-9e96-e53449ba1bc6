#!/usr/bin/env python3
"""
Simple script to run the Data Analysis LangGraph backend server.
"""

import os
import subprocess
import sys
from pathlib import Path


def main():
    """Start the server using the best available method."""
    print("🚀 Starting Data Analysis LangGraph Backend Server...")
    print("📍 Server will be available at: http://localhost:2024")
    print("📍 LangGraph Studio UI will open automatically")
    print("📍 API endpoints: http://localhost:2024")
    print("\n" + "=" * 50)

    # Change to backend directory
    backend_dir = Path(__file__).parent
    os.chdir(backend_dir)

    # Check if .env file exists
    if not Path(".env").exists():
        print("⚠️  No .env file found. Creating from template...")
        if Path(".env.example").exists():
            import shutil

            shutil.copy(".env.example", ".env")
            print(
                "📝 Created .env file. Please edit it and add your OpenAI API key."
            )
            print("Then run this script again.")
            return

    try:
        # Start with uvicorn directly (100% free)
        print("🔄 Starting with uvicorn...")
        cmd = [
            "uvicorn",
            "src.langgraph_da_backend.app:app",
            "--host",
            "0.0.0.0",
            "--port",
            "2024",
            "--reload",
        ]
        subprocess.run(cmd, check=True)

    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Could not start server with uvicorn.")
        print("\n📋 Please try manually:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Install FastAPI: pip install fastapi uvicorn")
        print(
            "3. Run: uvicorn src.langgraph_da_backend.app:app --host 0.0.0.0 --port 2024 --reload"
        )
        sys.exit(1)

    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
