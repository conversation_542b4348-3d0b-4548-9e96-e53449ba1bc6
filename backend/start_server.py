#!/usr/bin/env python3
"""
Startup script for the Data Analysis LangGraph backend server.
This script starts the LangGraph Studio server which serves both the API and the frontend.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Check if required dependencies are installed."""
    try:
        import langgraph
        import fastapi
        import uvicorn
        print("✅ Required dependencies found")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install requirements: pip install -r requirements.txt")
        return False

def check_env_file():
    """Check if .env file exists and create from template if not."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("📝 Creating .env file from template...")
            env_example.rename(env_file)
            print("⚠️  Please edit .env file and add your OpenAI API key")
            return False
        else:
            print("❌ No .env file found. Please create one with your OpenAI API key.")
            return False
    
    # Check if OpenAI API key is set
    with open(env_file) as f:
        content = f.read()
        if "your_openai_api_key_here" in content:
            print("⚠️  Please edit .env file and add your OpenAI API key")
            return False
    
    print("✅ Environment configuration found")
    return True

def start_server():
    """Start the LangGraph Studio server."""
    print("🚀 Starting Data Analysis LangGraph backend...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📍 API documentation: http://localhost:8000/docs")
    print("📍 Health check: http://localhost:8000/health")
    print("📍 Frontend (if built): http://localhost:8000/app")
    print("\n" + "="*50)
    
    try:
        # Start the LangGraph Studio server
        cmd = ["langgraph", "up", "--host", "0.0.0.0", "--port", "8000"]
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start server: {e}")
        print("\nTrying alternative startup method...")
        
        # Fallback: start with uvicorn directly
        try:
            cmd = ["uvicorn", "src.langgraph_da_backend.app:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
            subprocess.run(cmd, check=True)
        except subprocess.CalledProcessError as e2:
            print(f"❌ Alternative startup also failed: {e2}")
            print("\nPlease check that all dependencies are installed:")
            print("pip install -r requirements.txt")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

def main():
    """Main function."""
    print("🔍 Data Analysis LangGraph Backend Startup")
    print("="*50)
    
    # Change to backend directory
    backend_dir = Path(__file__).parent
    os.chdir(backend_dir)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check environment
    if not check_env_file():
        print("\n📋 Next steps:")
        print("1. Edit the .env file and add your OpenAI API key")
        print("2. Run this script again: python start_server.py")
        sys.exit(1)
    
    # Start the server
    start_server()

if __name__ == "__main__":
    main()
