# Data Analysis LangGraph Backend

This is the backend server for the Data Analysis LangGraph application. It exposes your LangGraph workflow as a web API that the frontend can interact with.

## Features

- 🔄 **LangGraph Workflow**: Exposes your data analysis workflow as a web API
- 🌐 **FastAPI Server**: RESTful API with automatic documentation
- 📊 **Real-time Streaming**: Supports streaming responses for real-time updates
- 💾 **State Persistence**: Uses SQLite checkpointer for workflow state management
- 🔧 **CORS Support**: Configured to work with the React frontend

## Quick Start

### 1. Install Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 2. Set Up Environment

```bash
# Copy the environment template
cp .env.example .env

# Edit .env and add your OpenAI API key
nano .env
```

### 3. Start the Server

```bash
# Use LangGraph dev (recommended)
langgraph dev
```

**Important**: Use `langgraph dev` instead of `langgraph up` or uvicorn directly. LangGraph dev automatically:
- Runs on port 2024 (matching frontend expectations)
- Provides all API endpoints (`/threads`, `/runs`, `/assistants`, etc.)
- Handles CORS automatically
- Opens LangGraph Studio UI for debugging

### 4. Verify Installation

```bash
# Test LangGraph Studio API endpoints
python test_langgraph_studio.py

# Test basic components
python simple_test.py
```

## API Endpoints

Once running, the server provides:

- **Health Check**: `GET /health`
- **API Documentation**: `GET /docs` (Swagger UI)
- **LangGraph API**: `/threads`, `/runs`, etc. (automatically generated)
- **Frontend**: `/app` (if frontend is built)

## Project Structure

```
backend/
├── src/langgraph_da_backend/
│   ├── app.py              # FastAPI application
│   ├── graph.py            # LangGraph workflow definition
│   ├── dummy_agent.py      # Your existing workflow logic
│   └── ...                 # Other modules
├── langgraph.json          # LangGraph configuration
├── requirements.txt        # Python dependencies
├── start_server.py         # Server startup script
├── test_backend.py         # Test suite
└── .env                    # Environment variables
```

## Configuration

### Open-Source Design

This project is designed to be **completely self-contained** and **open-source friendly**:

✅ **No External Dependencies**: Works without LangSmith or other paid monitoring services
✅ **Self-Managed**: All tracing and monitoring handled internally
✅ **Privacy-First**: No data sent to external services
✅ **Easy Deployment**: Simple setup with just OpenAI API key

### Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `LANGCHAIN_TRACING_V2`: Set to `false` for open-source compatibility (disabled by default)
- `HOST`: Server host (default: 0.0.0.0)
- `PORT`: Server port (default: 8000)

### LangGraph Configuration

The `langgraph.json` file configures:
- **Graph**: Points to your workflow in `graph.py`
- **HTTP App**: Points to the FastAPI app in `app.py`
- **Dependencies**: Includes the current directory

## Workflow Integration

Your existing `dummy_agent.py` workflow is integrated through:

1. **Graph Definition** (`graph.py`): Exposes your StateGraph as a web service
2. **State Management**: Uses the same AgentState and node functions
3. **Checkpointing**: Maintains state persistence across requests

## Frontend Integration

The backend is configured to work with the React frontend:

- **CORS**: Allows requests from `http://localhost:3000`
- **API Compatibility**: Matches the expected LangGraph SDK interface
- **Streaming**: Supports real-time updates via Server-Sent Events

## Development

### Testing

```bash
# Run the test suite
python test_backend.py

# Test individual components
python -c "from src.langgraph_da_backend.graph import graph; print('Graph loaded successfully')"
```

### Debugging

1. **Check Logs**: The server outputs detailed logs
2. **API Docs**: Visit `/docs` for interactive API testing
3. **Health Check**: Use `/health` to verify server status

### Common Issues

1. **Import Errors**: Make sure you're in the backend directory
2. **Missing API Key**: Check your `.env` file
3. **Port Conflicts**: Change the port in startup commands
4. **Dependencies**: Run `pip install -r requirements.txt`

## Production Deployment

For production deployment:

1. **Environment**: Set production environment variables
2. **Security**: Configure proper CORS origins
3. **Scaling**: Use a production ASGI server like Gunicorn
4. **Logging**: Use built-in logging and error handling for observability

## Troubleshooting

### Server Won't Start

1. Check if port 8000 is available: `lsof -i :8000`
2. Verify dependencies: `python test_backend.py`
3. Check environment: `cat .env`

### Frontend Can't Connect

1. Verify server is running: `curl http://localhost:8000/health`
2. Check CORS configuration in `app.py`
3. Ensure frontend is using the correct API URL

### Workflow Errors

1. Check the server logs for detailed error messages
2. Test the workflow locally: `python test_backend.py`
3. Verify your data files are accessible

## Support

If you encounter issues:

1. Check the server logs for error details
2. Run the test suite: `python test_backend.py`
3. Verify your environment configuration
4. Check that all dependencies are installed
